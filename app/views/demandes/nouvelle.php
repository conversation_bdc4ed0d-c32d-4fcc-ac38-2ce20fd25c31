<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nouvelle demande - Gestion des Congés</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/style.css">
</head>

<body class="bg-gray-50">
    <?php include_once APP_PATH . '/views/shared/sidebar_employe.php'; ?>

    <div class="md:ml-[240px] p-4 md:p-6 transition-all duration-200" id="main-content">
        <header class="mb-6">
            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div>
                    <h1 class="text-xl sm:text-2xl font-bold text-gray-800">Nouvelle demande</h1>
                    <p class="text-sm sm:text-base text-gray-600">Formulaire pour soumettre une nouvelle demande de
                        congé ou d'absence</p>
                </div>
                <a href="/mes-demandes"
                    class="text-purple-600 hover:text-purple-800 flex items-center gap-2 text-sm sm:text-base whitespace-nowrap">
                    <i class="fas fa-arrow-left"></i> Retour à mes demandes
                </a>
            </div>
        </header>

        <div class="bg-white p-6 rounded-lg shadow-sm">
            <?php if (isset($error)): ?>
            <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6">
                <p><?= htmlspecialchars($error) ?></p>
            </div>
            <?php endif; ?>

            <form action="/nouvelle-demande" method="POST" enctype="multipart/form-data">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <label for="type" class="block text-sm font-medium text-gray-700 mb-1">Type de demande *</label>
                        <select id="type" name="type"
                            class="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                            required>
                            <option value="" disabled selected>-- Sélectionnez un type --</option>
                            <option value="payé" <?= isset($type) && $type === 'payé' ? 'selected' : '' ?>>Congé payé
                            </option>
                            <option value="sans solde" <?= isset($type) && $type === 'sans solde' ? 'selected' : '' ?>>
                                Congé sans solde</option>
                            <option value="maladie" <?= isset($type) && $type === 'maladie' ? 'selected' : '' ?>>Congé
                                maladie</option>
                            <option value="exceptionnel"
                                <?= isset($type) && $type === 'exceptionnel' ? 'selected' : '' ?>>Congé exceptionnel
                            </option>
                            <option value="familial" <?= isset($type) && $type === 'familial' ? 'selected' : '' ?>>Congé
                                familial</option>
                        </select>
                    </div>

                    <div>
                        <label for="priorite" class="block text-sm font-medium text-gray-700 mb-1">Priorité</label>
                        <select id="priorite" name="priorite"
                            class="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                            <option value="normale" selected>Normale</option>
                            <option value="haute" <?= isset($priorite) && $priorite === 'haute' ? 'selected' : '' ?>>
                                Haute</option>
                            <option value="urgente"
                                <?= isset($priorite) && $priorite === 'urgente' ? 'selected' : '' ?>>Urgente</option>
                        </select>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6 mb-6">
                    <div>
                        <label for="date_debut" class="block text-sm font-medium text-gray-700 mb-1">Date de début
                            *</label>
                        <input type="date" id="date_debut" name="date_debut"
                            value="<?= isset($date_debut) ? htmlspecialchars($date_debut) : '' ?>"
                            class="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent min-h-[44px]"
                            required>
                        <div class="mt-2 flex flex-wrap items-center">
                            <div class="flex items-center mr-2 mb-2 sm:mb-0">
                                <input type="checkbox" id="demi_jour_debut" name="demi_jour_debut"
                                    class="h-5 w-5 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                                <label for="demi_jour_debut"
                                    class="ml-2 block text-sm text-gray-700">Demi-journée</label>
                            </div>
                            <select id="periode_debut" name="periode_debut"
                                class="pl-2 pr-6 py-1 text-sm border-gray-300 rounded-md min-h-[36px]" disabled>
                                <option value="matin">Matin</option>
                                <option value="apres-midi">Après-midi</option>
                            </select>
                        </div>
                    </div>
                    <div>
                        <label for="date_fin" class="block text-sm font-medium text-gray-700 mb-1">Date de fin *</label>
                        <input type="date" id="date_fin" name="date_fin"
                            value="<?= isset($date_fin) ? htmlspecialchars($date_fin) : '' ?>"
                            class="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent min-h-[44px]"
                            required>
                        <div class="mt-2 flex flex-wrap items-center">
                            <div class="flex items-center mr-2 mb-2 sm:mb-0">
                                <input type="checkbox" id="demi_jour_fin" name="demi_jour_fin"
                                    class="h-5 w-5 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                                <label for="demi_jour_fin" class="ml-2 block text-sm text-gray-700">Demi-journée</label>
                            </div>
                            <select id="periode_fin" name="periode_fin"
                                class="pl-2 pr-6 py-1 text-sm border-gray-300 rounded-md min-h-[36px]" disabled>
                                <option value="matin">Matin</option>
                                <option value="apres-midi">Après-midi</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="mb-6">
                    <label for="motif" class="block text-sm font-medium text-gray-700 mb-1">Motif / Commentaires</label>
                    <textarea id="motif" name="motif" rows="4"
                        class="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"><?= isset($motif) ? htmlspecialchars($motif) : '' ?></textarea>
                </div>
                <!--
                <div class="mb-6">
                    <label for="justificatif" class="block text-sm font-medium text-gray-700 mb-1">Justificatif (si
                        nécessaire)</label>
                    <div class="mt-1 flex flex-wrap items-center">
                        <label for="justificatif"
                            class="relative cursor-pointer bg-white rounded-md font-medium text-purple-600 hover:text-purple-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-purple-500 mb-2 sm:mb-0">
                            <span class="border border-gray-300 rounded-md px-3 py-2 text-sm inline-block min-h-[44px] flex items-center">Choisir un fichier</span>
                            <input id="justificatif" name="justificatif" type="file" class="sr-only"
                                accept=".pdf,.doc,.docx,.jpg,.jpeg,.png">
                        </label>
                        <span class="ml-0 sm:ml-2 text-sm text-gray-500 w-full sm:w-auto" id="file-name">Aucun fichier sélectionné</span>
                    </div>
                    <p class="mt-1 text-xs text-gray-500">Formats acceptés: PDF, Word, JPG, PNG (max. 5 MB)</p>
                </div> -->

                <div id="calculJours" class="mb-6 p-4 bg-gray-50 rounded-lg hidden">
                    <p class="text-sm text-gray-600">Votre demande porte sur <span id="nbJours"
                            class="font-bold">0</span> jour(s)</p>
                </div>

                <!-- Form Action Buttons -->
                <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2 sm:gap-4">
                    <!-- Policy Rules Button -->
                    <button type="button" onclick="openPolicyModal()"
                        class="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition text-sm flex items-center gap-2 mb-2 sm:mb-0">
                        <i class="fas fa-book mr-1"></i> Règles de congé
                    </button>

                    <!-- Action Buttons -->
                    <div class="flex flex-col sm:flex-row gap-2 sm:gap-4">
                        <a href="/mes-demandes"
                            class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 text-center min-h-[44px] flex items-center justify-center mb-2 sm:mb-0">Annuler</a>
                        <button type="submit"
                            class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 min-h-[44px]">Soumettre
                            la
                            demande</button>
                    </div>
                </div>
            </form>
        </div>


    <!-- Policy Modal -->
    <div id="policyModal"
        class="fixed inset-0 bg-gray-500 bg-opacity-75 hidden flex items-center justify-center z-50 p-4 transition-all duration-300 ease-in-out">
        <div
            class="modal-card bg-white rounded-lg shadow-xl max-w-lg w-full mx-4 transform transition-all duration-300 ease-in-out scale-95 opacity-0">
            <!-- Modal Header -->
            <div class="bg-purple-600 px-6 py-4 flex items-center justify-between">
                <div class="flex items-center">
                    <div
                        class="flex-shrink-0 w-8 h-8 rounded-full bg-white bg-opacity-20 flex items-center justify-center mr-3">
                        <i class="fas fa-book text-white text-lg"></i>
                    </div>
                    <h3 class="text-lg font-medium text-white">Politique de Congés</h3>
                </div>
                <button type="button" class="text-white hover:text-gray-200 transition-colors duration-200"
                    onclick="closePolicyModal()">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- Modal Body -->
            <div class="p-6">
                <div class="space-y-4">
                    <h4 class="font-semibold text-gray-800 mb-4">Rappel des règles :</h4>
                    <div class="space-y-4">
                        <div class="flex items-start p-4 bg-purple-50 rounded-lg border border-purple-200">
                            <div
                                class="flex-shrink-0 w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center mr-3 mt-0.5">
                                <i class="fas fa-calendar-check text-purple-600 text-sm"></i>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-purple-800 mb-1">Congés payés</p>
                                <p class="text-sm text-purple-700">Les congés payés doivent être posés au moins 7 jours
                                    à l'avance.</p>
                            </div>
                        </div>

                        <div class="flex items-start p-4 bg-orange-50 rounded-lg border border-orange-200">
                            <div
                                class="flex-shrink-0 w-8 h-8 rounded-full bg-orange-100 flex items-center justify-center mr-3 mt-0.5">
                                <i class="fas fa-exclamation-triangle text-orange-600 text-sm"></i>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-orange-800 mb-1">Congés exceptionnels</p>
                                <p class="text-sm text-orange-700">Les congés exceptionnels sont limités à 3 jours
                                    maximum.</p>
                            </div>
                        </div>

                        <div class="flex items-start p-4 bg-blue-50 rounded-lg border border-blue-200">
                            <div
                                class="flex-shrink-0 w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3 mt-0.5">
                                <i class="fas fa-balance-scale text-blue-600 text-sm"></i>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-blue-800 mb-1">Vérification du solde</p>
                                <p class="text-sm text-blue-700">Vérifiez votre solde avant de faire une demande.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modal Footer -->
            <div class="flex items-center justify-end p-6 border-t border-gray-200 bg-gray-50">
                <button type="button"
                    class="modal-btn-primary px-6 py-2 text-sm font-medium text-white bg-purple-600 rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors duration-200"
                    onclick="closePolicyModal()">
                    <i class="fas fa-check mr-2"></i>Compris
                </button>
            </div>
        </div>
    </div>
    <script src="/assets/js/nouvelle_demande.js"></script>

</body>

</html>