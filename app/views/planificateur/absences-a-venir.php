<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <title>Absences à venir - Gestion des Congés</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/style.css">
</head>

<body class="bg-gray-50">
    <?php include_once APP_PATH . '/views/shared/sidebar_planificateur.php'; ?>

    <!-- Modal for absence details -->
    <div id="absenceModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4 overflow-hidden">
            <div class="flex justify-between items-center p-4 border-b">
                <h3 class="text-lg font-semibold text-gray-900" id="modalTitle">Détails de l'absence</h3>
                <button type="button" class="text-gray-400 hover:text-gray-500" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="p-6" id="modalContent">
                <div class="flex flex-col space-y-4">
                    <div class="flex flex-col md:flex-row md:space-x-4">
                        <div class="w-full md:w-1/2">
                            <p class="text-sm font-medium text-gray-500">Employé</p>
                            <p class="text-base font-medium text-gray-900" id="modalEmployee">-</p>
                        </div>
                        <div class="w-full md:w-1/2">
                            <p class="text-sm font-medium text-gray-500">Type d'absence</p>
                            <p class="text-base font-medium text-gray-900" id="modalType">-</p>
                        </div>
                    </div>

                    <div class="flex flex-col md:flex-row md:space-x-4">
                        <div class="w-full md:w-1/2">
                            <p class="text-sm font-medium text-gray-500">Date de début</p>
                            <p class="text-base font-medium text-gray-900" id="modalStartDate">-</p>
                        </div>
                        <div class="w-full md:w-1/2">
                            <p class="text-sm font-medium text-gray-500">Date de fin</p>
                            <p class="text-base font-medium text-gray-900" id="modalEndDate">-</p>
                        </div>
                    </div>

                    <div class="flex flex-col md:flex-row md:space-x-4">
                        <div class="w-full md:w-1/2">
                            <p class="text-sm font-medium text-gray-500">Durée</p>
                            <p class="text-base font-medium text-gray-900" id="modalDuration">-</p>
                        </div>
                        <div class="w-full md:w-1/2">
                            <p class="text-sm font-medium text-gray-500">Statut</p>
                            <div id="modalStatus">-</div>
                        </div>
                    </div>

                    <div>
                        <p class="text-sm font-medium text-gray-500">Motif</p>
                        <p class="text-base font-medium text-gray-900" id="modalReason">-</p>
                    </div>

                    <div id="modalApprovalSection" class="hidden">
                        <p class="text-sm font-medium text-gray-500">Validé par</p>
                        <p class="text-base font-medium text-gray-900" id="modalApprovedBy">-</p>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-4 py-3 flex justify-end">
                <button type="button" class="bg-gray-200 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-300" onclick="closeModal()">
                    Fermer
                </button>
            </div>
        </div>
    </div>

    <div class="ml-[240px] p-6 transition-all duration-200" id="main-content">
        <header class="mb-6">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold text-gray-800">Absences à venir</h1>
                    <p class="text-gray-600">Consultez et gérez les absences planifiées</p>
                </div>
                <div class="flex space-x-2">
                    <button
                        class="px-3 py-1.5 bg-white border border-gray-300 rounded-md text-gray-700 text-sm hover:bg-gray-50">
                        <i class="fas fa-download mr-1"></i> Exporter
                    </button>
                </div>
            </div>
        </header>

        <!-- Filters -->
        <div class="card p-4 mb-6">
            <form action="/absences-a-venir" method="get" class="flex flex-wrap gap-4">
                <div>
                    <label for="departement" class="block text-sm font-medium text-gray-700 mb-1">Département</label>
                    <select id="departement" name="departement"
                        class="border border-gray-300 rounded-md px-3 py-1.5 text-gray-700 bg-white focus:outline-none focus:ring-2 focus:ring-teal-600 focus:border-transparent">
                        <option value="all">Tous les départements</option>
                        <?php foreach ($departments as $dept): ?>
                            <option value="<?= htmlspecialchars($dept) ?>" <?= $selectedDept === $dept ? 'selected' : '' ?>>
                                <?= htmlspecialchars($dept) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div>
                    <label for="date_debut" class="block text-sm font-medium text-gray-700 mb-1">Date début</label>
                    <input type="date" id="date_debut" name="date_debut" value="<?= htmlspecialchars($startDate) ?>"
                        class="border border-gray-300 rounded-md px-3 py-1.5 text-gray-700 bg-white focus:outline-none focus:ring-2 focus:ring-teal-600 focus:border-transparent">
                </div>
                <div>
                    <label for="date_fin" class="block text-sm font-medium text-gray-700 mb-1">Date fin</label>
                    <input type="date" id="date_fin" name="date_fin" value="<?= htmlspecialchars($endDate) ?>"
                        class="border border-gray-300 rounded-md px-3 py-1.5 text-gray-700 bg-white focus:outline-none focus:ring-2 focus:ring-teal-600 focus:border-transparent">
                </div>
                <div>
                    <label for="statut" class="block text-sm font-medium text-gray-700 mb-1">Statut</label>
                    <select id="statut" name="statut"
                        class="border border-gray-300 rounded-md px-3 py-1.5 text-gray-700 bg-white focus:outline-none focus:ring-2 focus:ring-teal-600 focus:border-transparent">
                        <option value="all">Tous les statuts</option>
                        <option value="acceptée" <?= $selectedStatus === 'acceptée' ? 'selected' : '' ?>>Approuvée</option>
                        <option value="en cours" <?= $selectedStatus === 'en cours' ? 'selected' : '' ?>>En attente</option>
                    </select>
                </div>
                <div class="flex items-end">
                    <button type="submit"
                        class="bg-teal-600 text-white px-4 py-1.5 rounded-md text-sm hover:bg-teal-700">
                        <i class="fas fa-filter mr-1"></i> Filtrer
                    </button>
                </div>
            </form>
        </div>

        <!-- Upcoming Absences Table -->
        <div class="card p-6 mb-6">
            <h2 class="text-lg font-semibold text-gray-800 mb-4">Absences à venir</h2>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead>
                        <tr>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employé</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Période</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Durée</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php if (empty($absences)): ?>
                            <tr>
                                <td colspan="6" class="px-4 py-4 text-center text-gray-500">Aucune absence à venir</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($absences as $absence): ?>
                                <?php
                                    $startDate = new DateTime($absence['date_debut']);
                                    $endDate = new DateTime($absence['date_fin']);
                                    $formattedStart = $startDate->format('d/m/Y');
                                    $formattedEnd = $endDate->format('d/m/Y');
                                    $period = $formattedStart;
                                    if ($formattedStart !== $formattedEnd) {
                                        $period .= ' au ' . $formattedEnd;
                                    }
                                ?>
                                <tr>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <?= htmlspecialchars($absence['nom_complet']) ?>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <?= htmlspecialchars($absence['type_formatted']) ?>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <?= $period ?>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <?= $absence['duree'] ?> jour<?= $absence['duree'] > 1 ? 's' : '' ?>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                            <?= $absence['statut'] === 'acceptée' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' ?>">
                                            <?= htmlspecialchars($absence['statut_formatted']) ?>
                                        </span>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-sm">
                                        <button type="button" onclick="showAbsenceDetails(<?= $absence['id'] ?>)" class="text-teal-600 hover:text-teal-800 mr-2">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <?php if ($absence['statut'] === 'en cours'): ?>
                                            <a href="/demandes/<?= $absence['id'] ?>/approve" class="text-green-600 hover:text-green-800 mr-2">
                                                <i class="fas fa-check"></i>
                                            </a>
                                            <a href="/demandes/<?= $absence['id'] ?>/reject" class="text-red-600 hover:text-red-800">
                                                <i class="fas fa-times"></i>
                                            </a>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Upcoming Holidays -->
        <div class="card p-6">
            <h2 class="text-lg font-semibold text-gray-800 mb-4">Prochains jours fériés</h2>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead>
                        <tr>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nom</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pays</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Récurrent</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php if (empty($holidays)): ?>
                            <tr>
                                <td colspan="4" class="px-4 py-4 text-center text-gray-500">Aucun jour férié à venir</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($holidays as $holiday): ?>
                                <?php
                                    $holidayDate = new DateTime($holiday['date']);
                                    $formattedDate = $holidayDate->format('d/m/Y');
                                ?>
                                <tr>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <?= htmlspecialchars($holiday['nom']) ?>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <?= $formattedDate ?>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <?= htmlspecialchars($holiday['pays']) ?>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <?= $holiday['est_recurrent'] ? 'Oui' : 'Non' ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        // Function to show the modal with absence details
        function showAbsenceDetails(absenceId) {
            // Show loading state
            document.getElementById('modalEmployee').textContent = 'Chargement...';
            document.getElementById('modalType').textContent = 'Chargement...';
            document.getElementById('modalStartDate').textContent = 'Chargement...';
            document.getElementById('modalEndDate').textContent = 'Chargement...';
            document.getElementById('modalDuration').textContent = 'Chargement...';
            document.getElementById('modalStatus').innerHTML = 'Chargement...';
            document.getElementById('modalReason').textContent = 'Chargement...';

            // Show the modal
            document.getElementById('absenceModal').classList.remove('hidden');

            // Fetch absence details using AJAX
            console.log(`Fetching absence details for ID: ${absenceId}`);

            // Use the full URL to avoid any path issues
            const apiUrl = window.location.origin + `/api/absences/${absenceId}`;
            console.log('API URL:', apiUrl);

            fetch(apiUrl)
                .then(response => {
                    console.log('Response status:', response.status);
                    if (!response.ok) {
                        throw new Error(`Erreur lors de la récupération des détails (${response.status})`);
                    }
                    return response.json();
                })
                .then(data => {
                    // Populate modal with absence details
                    document.getElementById('modalEmployee').textContent = data.nom_complet || '-';
                    document.getElementById('modalType').textContent = data.type_formatted || '-';

                    // Format dates
                    const startDate = new Date(data.date_debut);
                    const endDate = new Date(data.date_fin);
                    document.getElementById('modalStartDate').textContent = startDate.toLocaleDateString('fr-FR') || '-';
                    document.getElementById('modalEndDate').textContent = endDate.toLocaleDateString('fr-FR') || '-';

                    // Duration
                    document.getElementById('modalDuration').textContent = `${data.duree} jour${data.duree > 1 ? 's' : ''}`;

                    // Status with appropriate styling
                    let statusHtml = '';
                    if (data.statut === 'acceptée') {
                        statusHtml = `<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">${data.statut_formatted}</span>`;
                    } else if (data.statut === 'en cours') {
                        statusHtml = `<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">${data.statut_formatted}</span>`;
                    } else {
                        statusHtml = `<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">${data.statut_formatted}</span>`;
                    }
                    document.getElementById('modalStatus').innerHTML = statusHtml;

                    // Motif
                    document.getElementById('modalReason').textContent = data.motif || 'Aucun motif spécifié';

                    // Show approval info if available
                    if (data.valide_par && data.valideur_nom && data.valideur_prenom) {
                        document.getElementById('modalApprovalSection').classList.remove('hidden');
                        document.getElementById('modalApprovedBy').textContent = `${data.valideur_prenom} ${data.valideur_nom}`;
                    } else if (data.valide_par) {
                        document.getElementById('modalApprovalSection').classList.remove('hidden');
                        document.getElementById('modalApprovedBy').textContent = `ID: ${data.valide_par}`;
                    } else {
                        document.getElementById('modalApprovalSection').classList.add('hidden');
                    }
                })
                .catch(error => {
                    console.error('Error fetching absence details:', error);
                    console.error('Error message:', error.message);
                    document.getElementById('modalContent').innerHTML = `
                        <div class="text-center py-6">
                            <div class="text-red-500 mb-2"><i class="fas fa-exclamation-circle text-xl"></i></div>
                            <p class="text-gray-700">Une erreur est survenue lors de la récupération des détails.</p>
                            <p class="text-gray-500 text-sm mt-2">Veuillez réessayer plus tard.</p>
                            <p class="text-gray-500 text-xs mt-2">Erreur: ${error.message}</p>
                        </div>
                    `;
                });
        }

        // Function to close the modal
        function closeModal() {
            document.getElementById('absenceModal').classList.add('hidden');
        }

        // Close modal when clicking outside of it
        document.getElementById('absenceModal').addEventListener('click', function(event) {
            if (event.target === this) {
                closeModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape' && !document.getElementById('absenceModal').classList.contains('hidden')) {
                closeModal();
            }
        });
    </script>
</body>

</html>
