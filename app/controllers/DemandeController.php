<?php
// DemandeController to handle all demande-related operations

class <PERSON>mandeController extends Controller {
    private $demandeModel;
    private $userModel;
    private $leaveBalanceModel;
    private $departmentLeaveBalanceModel;

    public function __construct() {
        // Check if user is logged in
        Auth::requireLogin();

        // Load models
        $this->demandeModel = new DemandeModel();
        $this->userModel = new UserModel();
        $this->leaveBalanceModel = new LeaveBalanceModel();
        $this->departmentLeaveBalanceModel = new DepartmentLeaveBalanceModel();
    }

    // List all demandes for the logged-in user
    public function liste() {
        try {
            // Debug: Check the user ID in the session
            error_log("Current user ID in session: " . ($_SESSION['user_id'] ?? 'Not set'));

            // Get filters from URL parameters
            $period = $_GET['period'] ?? 'all';
            $status = $_GET['status'] ?? 'all';
            $type = $_GET['type'] ?? 'all';
            $search = $_GET['search'] ?? '';
            $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
            $perPage = isset($_GET['per_page']) ? max(5, min(50, intval($_GET['per_page']))) : 10;
            $sortBy = $_GET['sort_by'] ?? 'date_demande';
            $sortOrder = $_GET['sort_order'] ?? 'DESC';

            // Debug: Log the query parameters
            error_log("Query parameters: period=$period, status=$status, type=$type, search=$search, page=$page, perPage=$perPage, sortBy=$sortBy, sortOrder=$sortOrder");

            // Get user's demandes from model with filters, search, pagination and sorting
            $result = $this->demandeModel->getDemandesForUserWithFilters(
                $_SESSION['user_id'],
                $period,
                $status,
                $type,
                $search,
                $page,
                $perPage,
                $sortBy,
                $sortOrder
            );

            // Extract demandes and pagination info
            $demandes = $result['demandes'];
            $pagination = $result['pagination'];

            // Check if we have any results
            if ($demandes === false) {
                // Handle database error
                $demandes = [];
                $error = "Une erreur est survenue lors de la récupération des demandes.";
                $this->view('demandes/liste', [
                    'demandes' => $demandes,
                    'error' => $error,
                    'filters' => [
                        'period' => $period,
                        'status' => $status,
                        'type' => $type,
                        'search' => $search
                    ],
                    'pagination' => [
                        'total' => 0,
                        'perPage' => $perPage,
                        'currentPage' => $page,
                        'totalPages' => 0
                    ],
                    'sorting' => [
                        'sortBy' => $sortBy,
                        'sortOrder' => $sortOrder
                    ]
                ]);
                return;
            }

            // Check if success message or error passed via GET parameters
            $success = $_GET['success'] ?? null;
            $error = $_GET['error'] ?? null;

            // Determine which view to use based on the URL
            $viewPath = 'demandes/liste';

            // Check if the request is coming from the planificateur route
            if (strpos($_SERVER['REQUEST_URI'], '/planificateur/mes-demandes') === 0) {
                $viewPath = 'planificateur/mes_demandes';
            }
            // Check if the request is coming from the responsable route
            elseif (strpos($_SERVER['REQUEST_URI'], '/responsable/mes-demandes') === 0) {
                $viewPath = 'responsable/mes_demandes';
            }

            $this->view($viewPath, [
                'demandes' => $demandes,
                'success' => $success,
                'error' => $error,
                'filters' => [
                    'period' => $period,
                    'status' => $status,
                    'type' => $type,
                    'search' => $search
                ],
                'pagination' => $pagination,
                'sorting' => [
                    'sortBy' => $sortBy,
                    'sortOrder' => $sortOrder
                ]
            ]);

        } catch (Exception $e) {
            // Determine which view to use based on the URL
            $viewPath = 'demandes/liste';

            // Check if the request is coming from the planificateur route
            if (strpos($_SERVER['REQUEST_URI'], '/planificateur/mes-demandes') === 0) {
                $viewPath = 'planificateur/mes_demandes';
            }
            // Check if the request is coming from the responsable route
            elseif (strpos($_SERVER['REQUEST_URI'], '/responsable/mes-demandes') === 0) {
                $viewPath = 'responsable/mes_demandes';
            }

            // Handle any unexpected exceptions
            $this->view($viewPath, [
                'demandes' => [],
                'error' => 'Une erreur système est survenue. Veuillez réessayer plus tard.',
                'filters' => [
                    'period' => 'all',
                    'status' => 'all',
                    'type' => 'all',
                    'search' => ''
                ],
                'pagination' => [
                    'total' => 0,
                    'perPage' => 10,
                    'currentPage' => 1,
                    'totalPages' => 0
                ],
                'sorting' => [
                    'sortBy' => 'date_demande',
                    'sortOrder' => 'DESC'
                ]
            ]);
        }
    }

    // Display form to create a new demande
    public function nouvelle() {
        // Get user's leave balances for display in the form
        $leaveBalances = $this->leaveBalanceModel->getUserLeaveBalances($_SESSION['user_id']);

        // Get next accrual date (first day of next month)
        $nextMonth = new DateTime('first day of next month');
        $nextAccrualDate = $nextMonth->format('Y-m-d');

        // Get user's role to determine accrual rate
        $user = $this->userModel->getUserById($_SESSION['user_id']);
        $accrualRate = 1.4; // Default for regular employees

        if ($user['role'] === 'responsable') {
            $accrualRate = 1.83;
        } elseif ($user['role'] === 'admin') {
            $accrualRate = 1.99;
        }

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Process form submission
            $type = $_POST['type'] ?? '';
            $dateDebut = $_POST['date_debut'] ?? '';
            $dateFin = $_POST['date_fin'] ?? '';
            $motif = $_POST['motif'] ?? '';

            // Handle half-day options
            $demiJourDebut = isset($_POST['demi_jour_debut']) ? 1 : 0;
            $demiJourFin = isset($_POST['demi_jour_fin']) ? 1 : 0;
            $periodeDebut = $_POST['periode_debut'] ?? 'matin';
            $periodeFin = $_POST['periode_fin'] ?? 'apres-midi';

            // Determine demi_type value
            $demiType = null;
            if ($demiJourDebut && $demiJourFin) {
                $demiType = $periodeDebut . ',' . $periodeFin;
            } elseif ($demiJourDebut) {
                $demiType = $periodeDebut;
            } elseif ($demiJourFin) {
                $demiType = $periodeFin;
            }

            // Set demi_journee flag
            $demiJournee = ($demiJourDebut || $demiJourFin) ? 1 : 0;

            // Validate input
            if (empty($type) || empty($dateDebut) || empty($dateFin)) {
                $this->view('demandes/nouvelle', [
                    'error' => 'Tous les champs obligatoires doivent être remplis',
                    'type' => $type,
                    'date_debut' => $dateDebut,
                    'date_fin' => $dateFin,
                    'motif' => $motif,
                    'demi_jour_debut' => $demiJourDebut,
                    'demi_jour_fin' => $demiJourFin,
                    'periode_debut' => $periodeDebut,
                    'periode_fin' => $periodeFin,
                    // Detailed leave balance information
                    'solde_paye' => $leaveBalances['payé']['remaining'],
                    'solde_paye_used' => $leaveBalances['payé']['used'],
                    'solde_paye_total' => $leaveBalances['payé']['total'],

                    'solde_exceptionnel' => $leaveBalances['exceptionnel']['remaining'],
                    'solde_exceptionnel_used' => $leaveBalances['exceptionnel']['used'],
                    'solde_exceptionnel_total' => $leaveBalances['exceptionnel']['total'],

                    'solde_sans_solde' => $leaveBalances['sans solde']['remaining'],
                    'solde_sans_solde_used' => $leaveBalances['sans solde']['used'],
                    'solde_sans_solde_total' => $leaveBalances['sans solde']['total'],

                    'solde_maladie' => $leaveBalances['maladie']['remaining'],
                    'solde_maladie_used' => $leaveBalances['maladie']['used'],
                    'solde_maladie_total' => $leaveBalances['maladie']['total'],

                    'solde_familial' => $leaveBalances['familial']['remaining'],
                    'solde_familial_used' => $leaveBalances['familial']['used'],
                    'solde_familial_total' => $leaveBalances['familial']['total'],

                    // Accrual information
                    'next_accrual_date' => $nextAccrualDate,
                    'accrual_rate' => $accrualRate
                ]);
                return;
            }

            // Validate dates
            try {
                // Try to validate and format dates
                if (!$this->isValidDate($dateDebut) || !$this->isValidDate($dateFin)) {
                    $this->view('demandes/nouvelle', [
                        'error' => 'Format de date invalide. Veuillez utiliser le format YYYY-MM-DD.',
                        'type' => $type,
                        'date_debut' => $dateDebut,
                        'date_fin' => $dateFin,
                        'motif' => $motif,
                        'solde_paye' => $leaveBalances['payé']['remaining'],
                        'solde_exceptionnel' => $leaveBalances['exceptionnel']['remaining'],
                        'solde_sans_solde' => $leaveBalances['sans solde']['remaining']
                    ]);
                    return;
                }

                $startDate = new DateTime($dateDebut);
                $endDate = new DateTime($dateFin);

                if ($endDate < $startDate) {
                    $this->view('demandes/nouvelle', [
                        'error' => 'La date de fin doit être postérieure ou égale à la date de début',
                        'type' => $type,
                        'date_debut' => $dateDebut,
                        'date_fin' => $dateFin,
                        'motif' => $motif,
                        'solde_paye' => $leaveBalances['payé']['remaining'],
                        'solde_exceptionnel' => $leaveBalances['exceptionnel']['remaining'],
                        'solde_sans_solde' => $leaveBalances['sans solde']['remaining']
                    ]);
                    return;
                }

                // Validate that dates are not in the past
                $today = new DateTime();
                $today->setTime(0, 0, 0); // Reset time to start of day for fair comparison

                if ($startDate < $today) {
                    $this->view('demandes/nouvelle', [
                        'error' => 'La date de début ne peut pas être dans le passé',
                        'type' => $type,
                        'date_debut' => $dateDebut,
                        'date_fin' => $dateFin,
                        'motif' => $motif,
                        // Detailed leave balance information
                        'solde_paye' => $leaveBalances['payé']['remaining'],
                        'solde_paye_used' => $leaveBalances['payé']['used'],
                        'solde_paye_total' => $leaveBalances['payé']['total'],

                        'solde_exceptionnel' => $leaveBalances['exceptionnel']['remaining'],
                        'solde_exceptionnel_used' => $leaveBalances['exceptionnel']['used'],
                        'solde_exceptionnel_total' => $leaveBalances['exceptionnel']['total'],

                        'solde_sans_solde' => $leaveBalances['sans solde']['remaining'],
                        'solde_sans_solde_used' => $leaveBalances['sans solde']['used'],
                        'solde_sans_solde_total' => $leaveBalances['sans solde']['total'],

                        'solde_maladie' => $leaveBalances['maladie']['remaining'],
                        'solde_maladie_used' => $leaveBalances['maladie']['used'],
                        'solde_maladie_total' => $leaveBalances['maladie']['total'],

                        'solde_familial' => $leaveBalances['familial']['remaining'],
                        'solde_familial_used' => $leaveBalances['familial']['used'],
                        'solde_familial_total' => $leaveBalances['familial']['total'],

                        // Accrual information
                        'next_accrual_date' => $nextAccrualDate,
                        'accrual_rate' => $accrualRate
                    ]);
                    return;
                }

                // Calculate the number of days for this request
                $requestDays = $this->demandeModel->calculateLeaveDays($dateDebut, $dateFin, $demiJournee);

                // VALIDATION RULE 1: For paid leave requests, require at least 7 days advance notice
                if ($type === 'payé') {
                    $requestDate = new DateTime(); // Current date (request submission date)
                    $daysDifference = $requestDate->diff($startDate)->days;

                    if ($daysDifference < 7) {
                        $this->view('demandes/nouvelle', [
                            'error' => 'Les congés payés doivent être demandés au moins 7 jours à l\'avance.',
                            'type' => $type,
                            'date_debut' => $dateDebut,
                            'date_fin' => $dateFin,
                            'motif' => $motif,
                            'demi_jour_debut' => $demiJourDebut,
                            'demi_jour_fin' => $demiJourFin,
                            'periode_debut' => $periodeDebut,
                            'periode_fin' => $periodeFin,
                            'solde_paye' => $leaveBalances['payé']['remaining'],
                            'solde_exceptionnel' => $leaveBalances['exceptionnel']['remaining'],
                            'solde_sans_solde' => $leaveBalances['sans solde']['remaining']
                        ]);
                        return;
                    }
                }

                // VALIDATION RULE 2: For exceptional leave requests, limit to 3 days maximum
                if ($type === 'exceptionnel' && $requestDays > 3) {
                    $this->view('demandes/nouvelle', [
                        'error' => 'Les congés exceptionnels sont limités à 3 jours maximum.',
                        'type' => $type,
                        'date_debut' => $dateDebut,
                        'date_fin' => $dateFin,
                        'motif' => $motif,
                        'demi_jour_debut' => $demiJourDebut,
                        'demi_jour_fin' => $demiJourFin,
                        'periode_debut' => $periodeDebut,
                        'periode_fin' => $periodeFin,
                        'solde_paye' => $leaveBalances['payé']['remaining'],
                        'solde_exceptionnel' => $leaveBalances['exceptionnel']['remaining'],
                        'solde_sans_solde' => $leaveBalances['sans solde']['remaining']
                    ]);
                    return;
                }

                // VALIDATION RULE 3: Check leave balance before allowing submission
                // Use the LeaveBalanceModel for more accurate balance checking
                $leaveBalanceModel = new LeaveBalanceModel();

                if (isset($leaveBalances[$type]) && $type !== 'sans solde') {
                    $remainingDays = $leaveBalances[$type]['remaining'];

                    // Check if user has sufficient balance (skip for sans solde which is unlimited)
                    if (!$leaveBalanceModel->hasSufficientBalance($_SESSION['user_id'], $type, $requestDays)) {
                        $this->view('demandes/nouvelle', [
                            'error' => "Solde insuffisant. Vous demandez {$requestDays} jour(s) mais il vous reste seulement {$remainingDays} jour(s) de congé {$this->demandeModel->formatLeaveType($type)}.",
                            'type' => $type,
                            'date_debut' => $dateDebut,
                            'date_fin' => $dateFin,
                            'motif' => $motif,
                            'demi_jour_debut' => $demiJourDebut,
                            'demi_jour_fin' => $demiJourFin,
                            'periode_debut' => $periodeDebut,
                            'periode_fin' => $periodeFin,
                            // Detailed leave balance information
                            'solde_paye' => $leaveBalances['payé']['remaining'],
                            'solde_paye_used' => $leaveBalances['payé']['used'],
                            'solde_paye_total' => $leaveBalances['payé']['total'],

                            'solde_exceptionnel' => $leaveBalances['exceptionnel']['remaining'],
                            'solde_exceptionnel_used' => $leaveBalances['exceptionnel']['used'],
                            'solde_exceptionnel_total' => $leaveBalances['exceptionnel']['total'],

                            'solde_sans_solde' => $leaveBalances['sans solde']['remaining'],
                            'solde_sans_solde_used' => $leaveBalances['sans solde']['used'],
                            'solde_sans_solde_total' => $leaveBalances['sans solde']['total'],

                            'solde_maladie' => $leaveBalances['maladie']['remaining'],
                            'solde_maladie_used' => $leaveBalances['maladie']['used'],
                            'solde_maladie_total' => $leaveBalances['maladie']['total'],

                            'solde_familial' => $leaveBalances['familial']['remaining'],
                            'solde_familial_used' => $leaveBalances['familial']['used'],
                            'solde_familial_total' => $leaveBalances['familial']['total'],

                            // Accrual information
                            'next_accrual_date' => $nextAccrualDate,
                            'accrual_rate' => $accrualRate
                        ]);
                        return;
                    }
                }

                // VALIDATION RULE 4: Check department leave balance
                $userModel = new UserModel();
                $user = $userModel->getUserById($_SESSION['user_id']);
                $departmentName = $user['departement'];

                // Only check department balance for standard leave types (not special types like family, exceptional, sick)
                if (!in_array($type, ['familial', 'exceptionnel', 'maladie'])) {
                    // Get department balance first to show proper error message
                    $departmentBalance = $this->departmentLeaveBalanceModel->getDepartmentLeaveBalance($departmentName);

                    if (!$this->departmentLeaveBalanceModel->hasSufficientDepartmentBalance($departmentName, $requestDays, $type)) {
                        $remainingDays = $departmentBalance['remaining_days'] ?? 0;

                        $this->view('demandes/nouvelle', [
                            'error' => "Solde départemental insuffisant. Votre département ({$departmentName}) dispose de seulement {$remainingDays} jour(s) restants. Les congés exceptionnels, familiaux ou maladie ne sont pas soumis à cette limite.",
                            'type' => $type,
                            'date_debut' => $dateDebut,
                            'date_fin' => $dateFin,
                            'motif' => $motif,
                            'demi_jour_debut' => $demiJourDebut,
                            'demi_jour_fin' => $demiJourFin,
                            'periode_debut' => $periodeDebut,
                            'periode_fin' => $periodeFin,
                            // Detailed leave balance information
                            'solde_paye' => $leaveBalances['payé']['remaining'],
                            'solde_paye_used' => $leaveBalances['payé']['used'],
                            'solde_paye_total' => $leaveBalances['payé']['total'],

                            'solde_exceptionnel' => $leaveBalances['exceptionnel']['remaining'],
                            'solde_exceptionnel_used' => $leaveBalances['exceptionnel']['used'],
                            'solde_exceptionnel_total' => $leaveBalances['exceptionnel']['total'],

                            'solde_sans_solde' => $leaveBalances['sans solde']['remaining'],
                            'solde_sans_solde_used' => $leaveBalances['sans solde']['used'],
                            'solde_sans_solde_total' => $leaveBalances['sans solde']['total'],

                            'solde_maladie' => $leaveBalances['maladie']['remaining'],
                            'solde_maladie_used' => $leaveBalances['maladie']['used'],
                            'solde_maladie_total' => $leaveBalances['maladie']['total'],

                            'solde_familial' => $leaveBalances['familial']['remaining'],
                            'solde_familial_used' => $leaveBalances['familial']['used'],
                            'solde_familial_total' => $leaveBalances['familial']['total'],

                            // Accrual information
                            'next_accrual_date' => $nextAccrualDate,
                            'accrual_rate' => $accrualRate
                        ]);
                        return;
                    }
                }

                // Handle file upload if present
                $justificatif = null;
                if (isset($_FILES['justificatif']) && $_FILES['justificatif']['error'] === UPLOAD_ERR_OK) {
                    $uploadDir = 'uploads/justificatifs/';

                    // Create directory if it doesn't exist
                    if (!file_exists($uploadDir)) {
                        mkdir($uploadDir, 0777, true);
                    }

                    // Generate unique filename
                    $fileExtension = pathinfo($_FILES['justificatif']['name'], PATHINFO_EXTENSION);
                    $fileName = 'justificatif_' . $_SESSION['user_id'] . '_' . time() . '.' . $fileExtension;
                    $targetFile = $uploadDir . $fileName;

                    // Check file size (max 5MB)
                    if ($_FILES['justificatif']['size'] > 5 * 1024 * 1024) {
                        $this->view('demandes/nouvelle', [
                            'error' => 'Le fichier est trop volumineux. La taille maximale est de 5 MB.',
                            'type' => $type,
                            'date_debut' => $dateDebut,
                            'date_fin' => $dateFin,
                            'motif' => $motif,
                            'demi_jour_debut' => $demiJourDebut,
                            'demi_jour_fin' => $demiJourFin,
                            'periode_debut' => $periodeDebut,
                            'periode_fin' => $periodeFin
                        ]);
                        return;
                    }

                    // Check file type
                    $allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'image/jpeg', 'image/png'];
                    if (!in_array($_FILES['justificatif']['type'], $allowedTypes)) {
                        $this->view('demandes/nouvelle', [
                            'error' => 'Type de fichier non autorisé. Les formats acceptés sont: PDF, Word, JPG, PNG.',
                            'type' => $type,
                            'date_debut' => $dateDebut,
                            'date_fin' => $dateFin,
                            'motif' => $motif,
                            'demi_jour_debut' => $demiJourDebut,
                            'demi_jour_fin' => $demiJourFin,
                            'periode_debut' => $periodeDebut,
                            'periode_fin' => $periodeFin
                        ]);
                        return;
                    }

                    // Move uploaded file
                    if (move_uploaded_file($_FILES['justificatif']['tmp_name'], $targetFile)) {
                        $justificatif = $fileName;
                    } else {
                        $this->view('demandes/nouvelle', [
                            'error' => 'Erreur lors du téléchargement du fichier.',
                            'type' => $type,
                            'date_debut' => $dateDebut,
                            'date_fin' => $dateFin,
                            'motif' => $motif,
                            'demi_jour_debut' => $demiJourDebut,
                            'demi_jour_fin' => $demiJourFin,
                            'periode_debut' => $periodeDebut,
                            'periode_fin' => $periodeFin
                        ]);
                        return;
                    }
                }

                // Create new demande using the model
                $success = $this->demandeModel->createDemande(
                    $_SESSION['user_id'],
                    $type,
                    $dateDebut,
                    $dateFin,
                    $motif,
                    $demiJournee,
                    $demiType,
                    $justificatif
                );

                if ($success) {
                    // Determine the redirect URL based on the request URL
                    $redirectPath = '/mes-demandes';
                    if (strpos($_SERVER['REQUEST_URI'], '/planificateur/nouvelle-demande') === 0) {
                        $redirectPath = '/planificateur/mes-demandes';
                    }
                    elseif (strpos($_SERVER['REQUEST_URI'], '/responsable/nouvelle-demande') === 0) {
                        $redirectPath = '/responsable/mes-demandes';
                    }

                    $redirectUrl = $this->buildRedirectUrl($redirectPath, 'success', 'La demande a été créée avec succès.');
                    $this->redirect($redirectUrl);
                } else {
                    // Determine which view to use based on the URL
                    $viewPath = 'demandes/nouvelle';
                    if (strpos($_SERVER['REQUEST_URI'], '/planificateur/nouvelle-demande') === 0) {
                        $viewPath = 'planificateur/nouvelle_demande';
                    }
                    elseif (strpos($_SERVER['REQUEST_URI'], '/responsable/nouvelle-demande') === 0) {
                        $viewPath = 'responsable/nouvelle_demande';
                    }

                    $this->view($viewPath, [
                        'error' => 'Erreur lors de la création de la demande',
                        'type' => $type,
                        'date_debut' => $dateDebut,
                        'date_fin' => $dateFin,
                        'motif' => $motif,
                        'demi_jour_debut' => $demiJourDebut,
                        'demi_jour_fin' => $demiJourFin,
                        'periode_debut' => $periodeDebut,
                        'periode_fin' => $periodeFin,
                        // Detailed leave balance information
                        'solde_paye' => $leaveBalances['payé']['remaining'],
                        'solde_paye_used' => $leaveBalances['payé']['used'],
                        'solde_paye_total' => $leaveBalances['payé']['total'],

                        'solde_exceptionnel' => $leaveBalances['exceptionnel']['remaining'],
                        'solde_exceptionnel_used' => $leaveBalances['exceptionnel']['used'],
                        'solde_exceptionnel_total' => $leaveBalances['exceptionnel']['total'],

                        'solde_sans_solde' => $leaveBalances['sans solde']['remaining'],
                        'solde_sans_solde_used' => $leaveBalances['sans solde']['used'],
                        'solde_sans_solde_total' => $leaveBalances['sans solde']['total'],

                        'solde_maladie' => $leaveBalances['maladie']['remaining'],
                        'solde_maladie_used' => $leaveBalances['maladie']['used'],
                        'solde_maladie_total' => $leaveBalances['maladie']['total'],

                        'solde_familial' => $leaveBalances['familial']['remaining'],
                        'solde_familial_used' => $leaveBalances['familial']['used'],
                        'solde_familial_total' => $leaveBalances['familial']['total'],

                        // Accrual information
                        'next_accrual_date' => $nextAccrualDate,
                        'accrual_rate' => $accrualRate
                    ]);
                }
            } catch (Exception $e) {
                $this->view('demandes/nouvelle', [
                    'error' => 'Format de date invalide',
                    'type' => $type,
                    'date_debut' => $dateDebut,
                    'date_fin' => $dateFin,
                    'motif' => $motif,
                    // Detailed leave balance information
                    'solde_paye' => $leaveBalances['payé']['remaining'],
                    'solde_paye_used' => $leaveBalances['payé']['used'],
                    'solde_paye_total' => $leaveBalances['payé']['total'],

                    'solde_exceptionnel' => $leaveBalances['exceptionnel']['remaining'],
                    'solde_exceptionnel_used' => $leaveBalances['exceptionnel']['used'],
                    'solde_exceptionnel_total' => $leaveBalances['exceptionnel']['total'],

                    'solde_sans_solde' => $leaveBalances['sans solde']['remaining'],
                    'solde_sans_solde_used' => $leaveBalances['sans solde']['used'],
                    'solde_sans_solde_total' => $leaveBalances['sans solde']['total'],

                    'solde_maladie' => $leaveBalances['maladie']['remaining'],
                    'solde_maladie_used' => $leaveBalances['maladie']['used'],
                    'solde_maladie_total' => $leaveBalances['maladie']['total'],

                    'solde_familial' => $leaveBalances['familial']['remaining'],
                    'solde_familial_used' => $leaveBalances['familial']['used'],
                    'solde_familial_total' => $leaveBalances['familial']['total'],

                    // Accrual information
                    'next_accrual_date' => $nextAccrualDate,
                    'accrual_rate' => $accrualRate
                ]);
            }
        } else {
            // Determine which view to use based on the URL
            $viewPath = 'demandes/nouvelle';

            // Check if the request is coming from the planificateur route
            if (strpos($_SERVER['REQUEST_URI'], '/planificateur/nouvelle-demande') === 0) {
                $viewPath = 'planificateur/nouvelle_demande';
            }
            // Check if the request is coming from the responsable route
            elseif (strpos($_SERVER['REQUEST_URI'], '/responsable/nouvelle-demande') === 0) {
                $viewPath = 'responsable/nouvelle_demande';
            }

            // Display form with leave balances
            $this->view($viewPath, [
                // Detailed leave balance information
                'solde_paye' => $leaveBalances['payé']['remaining'],
                'solde_paye_used' => $leaveBalances['payé']['used'],
                'solde_paye_total' => $leaveBalances['payé']['total'],

                'solde_exceptionnel' => $leaveBalances['exceptionnel']['remaining'],
                'solde_exceptionnel_used' => $leaveBalances['exceptionnel']['used'],
                'solde_exceptionnel_total' => $leaveBalances['exceptionnel']['total'],

                'solde_sans_solde' => $leaveBalances['sans solde']['remaining'],
                'solde_sans_solde_used' => $leaveBalances['sans solde']['used'],
                'solde_sans_solde_total' => $leaveBalances['sans solde']['total'],

                'solde_maladie' => $leaveBalances['maladie']['remaining'],
                'solde_maladie_used' => $leaveBalances['maladie']['used'],
                'solde_maladie_total' => $leaveBalances['maladie']['total'],

                'solde_familial' => $leaveBalances['familial']['remaining'],
                'solde_familial_used' => $leaveBalances['familial']['used'],
                'solde_familial_total' => $leaveBalances['familial']['total'],

                // Accrual information
                'next_accrual_date' => $nextAccrualDate,
                'accrual_rate' => $accrualRate
            ]);
        }
    }

    // Display form to edit an existing demande
    public function modifier() {
        $id = $_GET['id'] ?? 0;

        if (empty($id)) {
            $this->redirect('/mes-demandes');
            return;
        }

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Process form submission
            $type = $_POST['type'] ?? '';
            $dateDebut = $_POST['date_debut'] ?? '';
            $dateFin = $_POST['date_fin'] ?? '';
            $motif = $_POST['motif'] ?? '';

            // Handle half-day options
            $demiJourDebut = isset($_POST['demi_jour_debut']) ? 1 : 0;
            $demiJourFin = isset($_POST['demi_jour_fin']) ? 1 : 0;
            $periodeDebut = $_POST['periode_debut'] ?? 'matin';
            $periodeFin = $_POST['periode_fin'] ?? 'apres-midi';

            // Determine demi_type value
            $demiType = null;
            if ($demiJourDebut && $demiJourFin) {
                $demiType = $periodeDebut . ',' . $periodeFin;
            } elseif ($demiJourDebut) {
                $demiType = $periodeDebut;
            } elseif ($demiJourFin) {
                $demiType = $periodeFin;
            }

            // Set demi_journee flag
            $demiJournee = ($demiJourDebut || $demiJourFin) ? 1 : 0;

            // Validate input
            if (empty($type) || empty($dateDebut) || empty($dateFin)) {
                $this->view('demandes/modifier', [
                    'error' => 'Tous les champs obligatoires doivent être remplis',
                    'demande' => [
                        'id' => $id,
                        'type' => $type,
                        'date_debut' => $dateDebut,
                        'date_fin' => $dateFin,
                        'motif' => $motif,
                        'statut' => 'en cours',
                        'demi_journee' => $demiJournee,
                        'demi_type' => $demiType
                    ]
                ]);
                return;
            }

            try {
                // Validate dates
                $startDate = new DateTime($dateDebut);
                $endDate = new DateTime($dateFin);

                if ($endDate < $startDate) {
                    $this->view('demandes/modifier', [
                        'error' => 'La date de fin doit être postérieure ou égale à la date de début',
                        'demande' => [
                            'id' => $id,
                            'type' => $type,
                            'date_debut' => $dateDebut,
                            'date_fin' => $dateFin,
                            'motif' => $motif,
                            'statut' => 'en cours'
                        ]
                    ]);
                    return;
                }

                // Validate future dates for new leave requests
                $today = new DateTime();
                $today->setTime(0, 0, 0); // Reset time to start of day for fair comparison

                if ($startDate < $today) {
                    $this->view('demandes/modifier', [
                        'error' => 'La date de début ne peut pas être dans le passé',
                        'demande' => [
                            'id' => $id,
                            'type' => $type,
                            'date_debut' => $dateDebut,
                            'date_fin' => $dateFin,
                            'motif' => $motif,
                            'statut' => 'en cours'
                        ]
                    ]);
                    return;
                }

                // Check if the demande exists and can be modified
                $existingDemande = $this->demandeModel->getDemandeById($id, $_SESSION['user_id']);
                if (!$existingDemande) {
                    $this->redirect('/mes-demandes');
                    return;
                }

                // Only "en cours" demandes can be modified
                if (strtolower($existingDemande['statut']) !== 'en cours') {
                    $this->view('demandes/modifier', [
                        'error' => 'Cette demande ne peut plus être modifiée',
                        'demande' => $existingDemande
                    ]);
                    return;
                }

                // Handle file upload if present
                $justificatif = null;
                if (isset($_FILES['justificatif']) && $_FILES['justificatif']['error'] === UPLOAD_ERR_OK) {
                    $uploadDir = 'uploads/justificatifs/';

                    // Create directory if it doesn't exist
                    if (!file_exists($uploadDir)) {
                        mkdir($uploadDir, 0777, true);
                    }

                    // Generate unique filename
                    $fileExtension = pathinfo($_FILES['justificatif']['name'], PATHINFO_EXTENSION);
                    $fileName = 'justificatif_' . $_SESSION['user_id'] . '_' . time() . '.' . $fileExtension;
                    $targetFile = $uploadDir . $fileName;

                    // Check file size (max 5MB)
                    if ($_FILES['justificatif']['size'] > 5 * 1024 * 1024) {
                        $this->view('demandes/modifier', [
                            'error' => 'Le fichier est trop volumineux. La taille maximale est de 5 MB.',
                            'demande' => [
                                'id' => $id,
                                'type' => $type,
                                'date_debut' => $dateDebut,
                                'date_fin' => $dateFin,
                                'motif' => $motif,
                                'statut' => 'en cours',
                                'demi_journee' => $demiJournee,
                                'demi_type' => $demiType
                            ]
                        ]);
                        return;
                    }

                    // Check file type
                    $allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'image/jpeg', 'image/png'];
                    if (!in_array($_FILES['justificatif']['type'], $allowedTypes)) {
                        $this->view('demandes/modifier', [
                            'error' => 'Type de fichier non autorisé. Les formats acceptés sont: PDF, Word, JPG, PNG.',
                            'demande' => [
                                'id' => $id,
                                'type' => $type,
                                'date_debut' => $dateDebut,
                                'date_fin' => $dateFin,
                                'motif' => $motif,
                                'statut' => 'en cours',
                                'demi_journee' => $demiJournee,
                                'demi_type' => $demiType
                            ]
                        ]);
                        return;
                    }

                    // Move uploaded file
                    if (move_uploaded_file($_FILES['justificatif']['tmp_name'], $targetFile)) {
                        $justificatif = $fileName;
                    } else {
                        $this->view('demandes/modifier', [
                            'error' => 'Erreur lors du téléchargement du fichier.',
                            'demande' => [
                                'id' => $id,
                                'type' => $type,
                                'date_debut' => $dateDebut,
                                'date_fin' => $dateFin,
                                'motif' => $motif,
                                'statut' => 'en cours',
                                'demi_journee' => $demiJournee,
                                'demi_type' => $demiType
                            ]
                        ]);
                        return;
                    }
                }

                // Update demande using the model
                $success = $this->demandeModel->updateDemande(
                    $id,
                    $_SESSION['user_id'],
                    $type,
                    $dateDebut,
                    $dateFin,
                    $motif,
                    $demiJournee,
                    $demiType,
                    $justificatif
                );

                if ($success) {
                    $redirectUrl = $this->buildRedirectUrl('/mes-demandes', 'success', 'La demande a été modifiée avec succès.');
                    $this->redirect($redirectUrl);
                } else {
                    $this->view('demandes/modifier', [
                        'error' => 'Erreur lors de la modification de la demande',
                        'demande' => [
                            'id' => $id,
                            'type' => $type,
                            'date_debut' => $dateDebut,
                            'date_fin' => $dateFin,
                            'motif' => $motif,
                            'statut' => 'en cours',
                            'demi_journee' => $demiJournee,
                            'demi_type' => $demiType
                        ]
                    ]);
                }
            } catch (Exception $e) {
                $this->view('demandes/modifier', [
                    'error' => 'Format de date invalide',
                    'demande' => [
                        'id' => $id,
                        'type' => $type,
                        'date_debut' => $dateDebut,
                        'date_fin' => $dateFin,
                        'motif' => $motif,
                        'statut' => 'en cours'
                    ]
                ]);
            }
        } else {
            // Get demande details from database
            $demande = $this->demandeModel->getDemandeById($id, $_SESSION['user_id']);

            if (!$demande) {
                $this->redirect('/mes-demandes');
                return;
            }

            // Display form with demande details
            $this->view('demandes/modifier', ['demande' => $demande]);
        }
    }

    // Cancel a demande
    public function annuler() {
        // Ensure the request has a valid ID
        $id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

        if ($id <= 0) {
            $this->redirect($this->getRedirectUrl());
            return;
        }

        try {
            // First check if the demande exists and belongs to the current user
            $demande = $this->demandeModel->getDemandeById($id, $_SESSION['user_id']);

            if (!$demande) {
                // If demande doesn't exist or doesn't belong to user
                $this->redirect($this->getRedirectUrl());
                return;
            }

            // Check if the demande can be cancelled (allow both 'en cours' and 'acceptee' statuses)
            if (strtolower($demande['statut']) !== 'en cours' && strtolower($demande['statut']) !== 'acceptee') {
                // Preserve current filters and pagination
                $redirectUrl = $this->buildRedirectUrl($this->getRedirectUrl(), 'error', 'Vous ne pouvez pas annuler une demande qui a été refusée ou déjà annulée.');
                $this->redirect($redirectUrl);
                return;
            }

            // Cancel demande using the database model
            $success = $this->demandeModel->cancelDemande($id, $_SESSION['user_id']);

            // Redirect with appropriate message
            if ($success) {
                $redirectUrl = $this->buildRedirectUrl($this->getRedirectUrl(), 'success', 'La demande a été annulée avec succès.');
                $this->redirect($redirectUrl);
            } else {
                $redirectUrl = $this->buildRedirectUrl($this->getRedirectUrl(), 'error', 'Une erreur est survenue lors de l\'annulation de la demande.');
                $this->redirect($redirectUrl);
            }
        } catch (Exception $e) {
            // Handle any unexpected exceptions
            $redirectUrl = $this->buildRedirectUrl($this->getRedirectUrl(), 'error', 'Une erreur système est survenue. Veuillez réessayer plus tard.');
            $this->redirect($redirectUrl);
        }
    }

    // Role-specific cancel demande method for routes like /responsable/annuler-demande
    public function annulerDemande() {
        // Just call the regular annuler method
        $this->annuler();
    }

    /**
     * Display details of a demande
     */
    public function details() {
        // Ensure the request has a valid ID
        $id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

        if ($id <= 0) {
            $this->redirect($this->getRedirectUrl());
            return;
        }

        try {
            // Get demande details from database based on user role
            $demande = null;

            if ($_SESSION['role'] === 'admin') {
                // Admin can see all demandes
                $demande = $this->demandeModel->getDemandeById($id);
            } elseif ($_SESSION['role'] === 'responsable') {
                // First check if this is the responsable's own demande
                $ownDemande = $this->demandeModel->getDemandeById($id, $_SESSION['user_id']);

                if ($ownDemande) {
                    // If it's the responsable's own demande, use that
                    $demande = $ownDemande;
                } else {
                    // Otherwise, check if it's a demande from one of their team members
                    $demande = $this->demandeModel->getDemandeByIdForResponsable($id, $_SESSION['user_id']);
                }
            } elseif ($_SESSION['role'] === 'planificateur') {
                // First check if this is the planificateur's own demande
                $ownDemande = $this->demandeModel->getDemandeById($id, $_SESSION['user_id']);

                if ($ownDemande) {
                    // If it's the planificateur's own demande, use that
                    $demande = $ownDemande;
                } else {
                    // Otherwise, planificateur can see all demandes
                    $demande = $this->demandeModel->getDemandeById($id);
                }
            } else {
                // Employe can only see their own demandes
                $demande = $this->demandeModel->getDemandeById($id, $_SESSION['user_id']);
            }

            if (!$demande) {
                // If demande doesn't exist or user doesn't have permission to view it
                $this->redirect($this->getRedirectUrl());
                return;
            }

            // Determine which view to use based on the URL and role
            $viewPath = 'demandes/details';

            // Check if the request is coming from a specific planificateur route
            if (strpos($_SERVER['REQUEST_URI'], '/planificateur/details-demande') === 0) {
                $viewPath = 'planificateur/details_demande';
            }
            // Check if the request is coming from a specific responsable route
            elseif (strpos($_SERVER['REQUEST_URI'], '/responsable/details-demande') === 0) {
                $viewPath = 'responsable/details_demande';
            }

            // Display details view
            $this->view($viewPath, ['demande' => $demande]);
        } catch (Exception $e) {
            // Handle any unexpected exceptions
            $redirectUrl = $this->buildRedirectUrl($this->getRedirectUrl(), 'error', 'Une erreur système est survenue. Veuillez réessayer plus tard.');
            $this->redirect($redirectUrl);
        }
    }

    /**
     * Get the appropriate redirect URL based on user role
     */
    private function getRedirectUrl() {
        // Check if the request is coming from a specific planificateur route
        if (strpos($_SERVER['REQUEST_URI'], '/planificateur/mes-demandes') === 0 ||
            strpos($_SERVER['REQUEST_URI'], '/planificateur/details-demande') === 0 ||
            strpos($_SERVER['REQUEST_URI'], '/planificateur/annuler-demande') === 0) {
            return '/planificateur/mes-demandes';
        }
        // Check if the request is coming from a specific responsable route
        if (strpos($_SERVER['REQUEST_URI'], '/responsable/mes-demandes') === 0 ||
            strpos($_SERVER['REQUEST_URI'], '/responsable/details-demande') === 0 ||
            strpos($_SERVER['REQUEST_URI'], '/responsable/annuler-demande') === 0) {
            return '/responsable/mes-demandes';
        }

        switch ($_SESSION['role']) {
            case 'admin':
                return '/all-demandes';
            case 'responsable':
                // Check if we're in the context of the responsable's own requests
                if (strpos($_SERVER['REQUEST_URI'], '/responsable/mes-demandes') === 0 ||
                    (strpos($_SERVER['REQUEST_URI'], '/details-demande') === 0 && isset($_GET['id']))) {

                    // If we're coming from details page, check if it's the responsable's own demande
                    if (strpos($_SERVER['REQUEST_URI'], '/details-demande') === 0 && isset($_GET['id'])) {
                        $demandeId = (int)$_GET['id'];
                        $ownDemande = $this->demandeModel->getDemandeById($demandeId, $_SESSION['user_id']);

                        if ($ownDemande) {
                            return '/responsable/mes-demandes';
                        } else {
                            return '/responsable/demandes_approbation';
                        }
                    }

                    return '/responsable/mes-demandes';
                }
                return '/responsable/demandes_approbation';
            case 'planificateur':
                // Check if we're in the context of the planificateur's own requests
                if (strpos($_SERVER['REQUEST_URI'], '/planificateur/mes-demandes') === 0 ||
                    (strpos($_SERVER['REQUEST_URI'], '/details-demande') === 0 && isset($_GET['id']))) {

                    // If we're coming from details page, check if it's the planificateur's own demande
                    if (strpos($_SERVER['REQUEST_URI'], '/details-demande') === 0 && isset($_GET['id'])) {
                        $demandeId = (int)$_GET['id'];
                        $ownDemande = $this->demandeModel->getDemandeById($demandeId, $_SESSION['user_id']);

                        if ($ownDemande) {
                            return '/planificateur/mes-demandes';
                        } else {
                            return '/planificateur/demandes_approbation';
                        }
                    }

                    return '/planificateur/mes-demandes';
                }
                return '/absences-a-venir';
            case 'employe':
            default:
                return '/mes-demandes';
        }
    }

    /**
     * Helper method to build a redirect URL with preserved filters and a message
     *
     * @param string $baseUrl The base URL to redirect to
     * @param string $messageType The type of message (success or error)
     * @param string $message The message to display
     * @return string The complete redirect URL
     */
    private function buildRedirectUrl($baseUrl, $messageType, $message) {
        // Get current filters from URL
        $params = [];

        // Preserve current filters
        $filterParams = ['period', 'status', 'type', 'search', 'page', 'per_page', 'sort_by', 'sort_order'];
        foreach ($filterParams as $param) {
            if (isset($_GET[$param])) {
                $params[$param] = $_GET[$param];
            }
        }

        // Add message
        $params[$messageType] = $message;

        // Build URL
        $queryString = http_build_query($params);
        return $baseUrl . ($queryString ? '?' . $queryString : '');
    }

    // Helper method to validate date format
    private function isValidDate($date) {
        if (empty($date)) {
            return false;
        }

        // Try to validate the date format
        $format = 'Y-m-d'; // ISO format (YYYY-MM-DD)

        $parsedDate = DateTime::createFromFormat($format, $date);

        // Check if the date was successfully parsed and if it matches the input
        return $parsedDate && $parsedDate->format($format) === $date;
    }

    // Handle document request AJAX calls
    public function demande_document() {
        // Only handle POST requests
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            return;
        }

        // Check if it's an AJAX request
        if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || $_SERVER['HTTP_X_REQUESTED_WITH'] !== 'XMLHttpRequest') {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid request']);
            return;
        }

        try {
            // Get JSON input
            $input = json_decode(file_get_contents('php://input'), true);

            if (!$input) {
                throw new Exception('Invalid JSON data');
            }

            $type = $input['type'] ?? '';
            $documentName = $input['document_name'] ?? '';

            // Validate input
            if (empty($type) || empty($documentName)) {
                throw new Exception('Type et nom du document sont requis');
            }

            // Validate document type
            $allowedTypes = ['attestation_travail', 'fiche_paie', 'attestation_salaire'];
            if (!in_array($type, $allowedTypes)) {
                throw new Exception('Type de document non autorisé');
            }

            // Load DocumentRequestModel
            $documentRequestModel = new DocumentRequestModel();

            // Create the document request
            $success = $documentRequestModel->createDocumentRequest(
                $_SESSION['user_id'],
                $type,
                $documentName,
                null // No comments for now
            );

            if ($success) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Demande de document créée avec succès'
                ]);
            } else {
                throw new Exception('Erreur lors de la création de la demande');
            }

        } catch (Exception $e) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }
}
