-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.2
-- https://www.phpmyadmin.net/
--
-- Host: db
-- Generation Time: May 28, 2025 at 05:00 PM
-- Server version: 8.4.5
-- PHP Version: 8.2.27

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `gestion_conges`
--

-- --------------------------------------------------------

--
-- Table structure for table `demandes_conges`
--

CREATE TABLE `demandes_conges` (
  `id` int NOT NULL,
  `reference_demande` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `user_id` int NOT NULL,
  `type` varchar(20) COLLATE utf8mb4_general_ci NOT NULL,
  `date_debut` date NOT NULL,
  `date_fin` date NOT NULL,
  `statut` varchar(30) COLLATE utf8mb4_general_ci NOT NULL,
  `date_demande` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `date_decision` timestamp NULL DEFAULT NULL,
  `demi_journee` tinyint(1) DEFAULT '0',
  `demi_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `motif` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `justificatif` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `motif_rejet` text COLLATE utf8mb4_general_ci,
  `responsable_id` int DEFAULT NULL,
  `planificateur_id` int DEFAULT NULL,
  `date_approbation_responsable` timestamp NULL DEFAULT NULL,
  `date_approbation_planificateur` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `demandes_conges`
--

INSERT INTO `demandes_conges` (`id`, `reference_demande`, `user_id`, `type`, `date_debut`, `date_fin`, `statut`, `date_demande`, `date_decision`, `demi_journee`, `demi_type`, `motif`, `justificatif`, `motif_rejet`, `responsable_id`, `planificateur_id`, `date_approbation_responsable`, `date_approbation_planificateur`) VALUES
(1, '2805-EMP-1', 17, 'sans solde', '2025-06-25', '2025-06-29', 'refusee', '2025-05-28 11:08:27', '2025-05-28 11:09:08', 0, NULL, 'Maiores quis iure ad', NULL, 'Période de forte activité dans le service', 19, NULL, NULL, NULL),
(2, '2805-EMP-1', 17, 'exceptionnel', '2025-06-28', '2025-06-30', 'refusee', '2025-05-28 15:24:03', '2025-05-28 15:24:37', 0, NULL, 'Ad nulla in quisquam', NULL, 'Période de forte activité dans le service', 19, NULL, NULL, NULL),
(3, '2805-EMP-1', 17, 'payé', '2025-06-04', '2025-06-06', 'refusee', '2025-05-28 16:30:28', '2025-05-28 16:41:17', 0, NULL, 'Test demande for two-step approval', NULL, 'Période de forte activité dans le service', 19, NULL, NULL, NULL),
(4, '2805-EMP-1', 17, 'payé', '2025-06-04', '2025-06-06', 'refusee', '2025-05-28 16:30:28', '2025-05-28 16:41:20', 0, NULL, 'Test demande for two-step approval', NULL, 'Période de forte activité dans le service', 19, NULL, NULL, NULL),
(5, '2805-EMP-1', 17, 'payé', '2025-06-04', '2025-06-06', 'approuvee', '2025-05-28 16:32:47', '2025-05-28 16:42:58', 0, NULL, 'Test demande for two-step approval', NULL, NULL, 19, 18, '2025-05-28 16:32:47', '2025-05-28 16:42:58'),
(6, '2805-EMP-1', 17, 'payé', '2025-06-04', '2025-06-06', 'approuvee', '2025-05-28 16:33:13', '2025-05-28 16:33:13', 0, NULL, 'Test demande for two-step approval', NULL, NULL, 19, 18, '2025-05-28 16:33:13', '2025-05-28 16:33:13'),
(7, '2805-EMP-1', 17, 'payé', '2025-06-04', '2025-06-06', 'refusee', '2025-05-28 16:33:13', '2025-05-28 16:41:31', 0, NULL, 'Test demande for two-step approval', NULL, 'Période de forte activité dans le service', 19, NULL, NULL, NULL),
(8, '2805-EMP-1', 17, 'sans solde', '2025-05-30', '2025-06-02', 'refusee', '2025-05-28 16:40:36', '2025-05-28 16:43:59', 0, NULL, '', NULL, 'Solde de congés insuffisant', 19, NULL, NULL, NULL),
(9, '2805-EMP-1', 17, 'sans solde', '2025-05-29', '2025-05-29', 'approuvee', '2025-05-28 16:44:37', '2025-05-28 16:58:55', 1, 'matin', 'Dolore facilis ea oc', NULL, NULL, 19, 18, '2025-05-28 16:58:55', '2025-05-28 16:58:55');

-- --------------------------------------------------------

--
-- Table structure for table `department_leave_balances`
--

CREATE TABLE `department_leave_balances` (
  `id` int NOT NULL,
  `department_name` varchar(50) COLLATE utf8mb4_general_ci NOT NULL,
  `year` int NOT NULL,
  `total_days` float NOT NULL DEFAULT '15',
  `used_days` float NOT NULL DEFAULT '0',
  `remaining_days` float NOT NULL DEFAULT '15',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `department_leave_balances`
--

INSERT INTO `department_leave_balances` (`id`, `department_name`, `year`, `total_days`, `used_days`, `remaining_days`, `created_at`, `updated_at`) VALUES
(1, 'admin', 2025, 15, 0, 15, '2025-05-22 16:59:12', '2025-05-26 15:52:13'),
(2, 'Support client', 2025, 15, 7.5, 7, '2025-05-22 16:59:12', '2025-05-28 16:58:55'),
(3, 'Adminstration', 2025, 15, 0, 15, '2025-05-22 16:59:12', NULL),
(4, 'admin', 2024, 15, 0, 15, '2025-05-23 09:40:48', NULL),
(5, 'Support client', 2024, 15, 0, 15, '2025-05-23 09:40:48', NULL),
(6, 'Adminstration', 2024, 15, 0, 15, '2025-05-23 09:40:48', NULL),
(9, 'IT', 2025, 15, 0, 15, '2025-05-26 14:04:02', NULL),
(15, 'Administration', 2025, 15, 0, 15, '2025-05-26 15:49:19', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `documents`
--

CREATE TABLE `documents` (
  `id` int NOT NULL,
  `nom` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `lien` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `categorie` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'general',
  `ordre` int DEFAULT '0',
  `date_creation` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `date_modification` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `actif` tinyint(1) DEFAULT '1'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `documents`
--

INSERT INTO `documents` (`id`, `nom`, `lien`, `description`, `categorie`, `ordre`, `date_creation`, `date_modification`, `actif`) VALUES
(1, 'Politique de congés', '/documents/politique-conges.pdf', 'Règles et procédures pour les congés', 'general', 1, '2025-05-15 16:01:51', '2025-05-26 17:00:42', 1),
(2, 'Guide de l\'employé', '/documents/guide-employe.pdf', 'Informations générales pour les employés', 'general', 2, '2025-05-15 16:01:51', '2025-05-26 10:00:00', 1),
(6, 'Attestation de travail', '/documents/attestation-travail.pdf', 'Document officiel attestant votre emploi', 'general', 3, '2025-05-26 09:52:03', '2025-05-26 10:00:00', 1),
(7, 'Fiche de paie', '/documents/fiche-paie.pdf', 'Bulletin de salaire mensuel', 'general', 4, '2025-05-26 09:52:03', '2025-05-26 10:00:01', 1),
(8, 'Attestation de salaire', '/documents/attestation-salaire.pdf', 'Document certifiant votre rÃ©munÃ©ration', 'general', 5, '2025-05-26 09:52:03', '2025-05-26 10:00:01', 1);

--
-- Triggers `documents`
--
DELIMITER $$
CREATE TRIGGER `before_insert_documents` BEFORE INSERT ON `documents` FOR EACH ROW BEGIN
  DECLARE max_ordre INT;
  SELECT IFNULL(MAX(ordre), 0) INTO max_ordre FROM documents;
  SET NEW.ordre = max_ordre + 1;
END
$$
DELIMITER ;

-- --------------------------------------------------------

--
-- Table structure for table `document_requests`
--

CREATE TABLE `document_requests` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `type` varchar(50) COLLATE utf8mb4_general_ci NOT NULL,
  `document_name` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `comments` text COLLATE utf8mb4_general_ci,
  `status` enum('pending','processing','completed','rejected') COLLATE utf8mb4_general_ci DEFAULT 'pending',
  `date_requested` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `date_processed` timestamp NULL DEFAULT NULL,
  `processed_by` int DEFAULT NULL,
  `processing_comments` text COLLATE utf8mb4_general_ci,
  `file_path` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `document_requests`
--

INSERT INTO `document_requests` (`id`, `user_id`, `type`, `document_name`, `comments`, `status`, `date_requested`, `date_processed`, `processed_by`, `processing_comments`, `file_path`) VALUES
(1, 17, 'attestation_travail', 'Attestation de travail', NULL, 'pending', '2025-05-26 16:58:41', NULL, NULL, NULL, NULL),
(2, 17, 'attestation_travail', 'Attestation de travail', NULL, 'pending', '2025-05-27 06:35:18', NULL, NULL, NULL, NULL),
(3, 17, 'attestation_travail', 'Attestation de travail', NULL, 'pending', '2025-05-27 11:00:16', NULL, NULL, NULL, NULL),
(4, 17, 'attestation_travail', 'Attestation de travail', NULL, 'pending', '2025-05-28 08:32:22', NULL, NULL, NULL, NULL),
(5, 17, 'attestation_travail', 'Attestation de travail', NULL, 'pending', '2025-05-28 11:55:48', NULL, NULL, NULL, NULL),
(6, 17, 'attestation_travail', 'Attestation de travail', NULL, 'pending', '2025-05-28 12:05:10', NULL, NULL, NULL, NULL),
(7, 17, 'attestation_travail', 'Attestation de travail', NULL, 'pending', '2025-05-28 12:06:21', NULL, NULL, NULL, NULL),
(8, 17, 'attestation_travail', 'Attestation de travail', NULL, 'pending', '2025-05-28 12:06:25', NULL, NULL, NULL, NULL),
(9, 17, 'fiche_paie', 'Fiche de paie', NULL, 'pending', '2025-05-28 12:06:32', NULL, NULL, NULL, NULL),
(10, 17, 'attestation_salaire', 'Attestation de salaire', NULL, 'pending', '2025-05-28 12:06:34', NULL, NULL, NULL, NULL),
(11, 17, 'attestation_travail', 'Attestation de travail', NULL, 'pending', '2025-05-28 12:08:12', NULL, NULL, NULL, NULL),
(12, 17, 'attestation_travail', 'Attestation de travail', NULL, 'pending', '2025-05-28 12:10:13', NULL, NULL, NULL, NULL),
(13, 17, 'attestation_travail', 'Attestation de travail', NULL, 'pending', '2025-05-28 12:10:23', NULL, NULL, NULL, NULL),
(14, 17, 'attestation_travail', 'Attestation de travail', NULL, 'pending', '2025-05-28 13:28:25', NULL, NULL, NULL, NULL),
(15, 17, 'attestation_travail', 'Attestation de travail', NULL, 'pending', '2025-05-28 15:23:16', NULL, NULL, NULL, NULL),
(16, 17, 'attestation_travail', 'Attestation de travail', NULL, 'pending', '2025-05-28 15:23:30', NULL, NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `jours_feries`
--

CREATE TABLE `jours_feries` (
  `id` int NOT NULL,
  `nom` varchar(50) NOT NULL,
  `date` date NOT NULL,
  `est_recurrent` tinyint(1) NOT NULL,
  `date_creation` date NOT NULL,
  `pays` varchar(50) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Dumping data for table `jours_feries`
--

INSERT INTO `jours_feries` (`id`, `nom`, `date`, `est_recurrent`, `date_creation`, `pays`) VALUES
(1, 'Jour de l\'an', '2025-01-01', 1, '2025-05-20', 'France'),
(2, 'Lundi de Pâques', '2025-04-21', 1, '2025-05-20', 'France'),
(3, 'Fête du Travail', '2025-05-01', 1, '2025-05-20', 'France'),
(4, 'Victoire 1945', '2025-05-08', 1, '2025-05-20', 'France'),
(5, 'Ascension', '2025-05-29', 1, '2025-05-20', 'France'),
(6, 'Lundi de Pentecôte', '2025-06-09', 1, '2025-05-20', 'France'),
(7, 'Fête Nationale', '2025-07-14', 1, '2025-05-20', 'France'),
(8, 'Assomption', '2025-08-15', 1, '2025-05-20', 'France'),
(9, 'Toussaint', '2025-11-01', 1, '2025-05-20', 'France'),
(10, 'Armistice 1918', '2025-11-11', 1, '2025-05-20', 'France'),
(11, 'Noël', '2025-12-25', 1, '2025-05-20', 'France'),
(12, 'Jour de l\'an', '2025-01-01', 1, '2025-05-20', 'Tunisia'),
(13, 'Fête de l\'Indépendance', '2025-03-20', 1, '2025-05-20', 'Tunisia'),
(14, 'Aïd El Fitr', '2025-04-10', 1, '2025-05-20', 'Tunisia'),
(15, 'Aïd El Fitr (lendemain)', '2025-04-11', 1, '2025-05-20', 'Tunisia'),
(16, 'Fête du Travail', '2025-05-01', 1, '2025-05-20', 'Tunisia'),
(17, 'Aïd El Adha', '2025-06-17', 1, '2025-05-20', 'Tunisia'),
(18, 'Aïd El Adha (lendemain)', '2025-06-18', 1, '2025-05-20', 'Tunisia'),
(19, 'Fête de la République', '2025-07-25', 1, '2025-05-20', 'Tunisia'),
(20, 'Fête de la Femme', '2025-08-13', 1, '2025-05-20', 'Tunisia'),
(21, 'Mouled', '2025-10-06', 1, '2025-05-20', 'Tunisia');

-- --------------------------------------------------------

--
-- Table structure for table `leave_accrual_history`
--

CREATE TABLE `leave_accrual_history` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `leave_type` enum('payé','exceptionnel','sans solde','maladie','familial') COLLATE utf8mb4_general_ci NOT NULL,
  `accrual_date` date NOT NULL,
  `accrual_amount` float NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `leave_accrual_history`
--

INSERT INTO `leave_accrual_history` (`id`, `user_id`, `leave_type`, `accrual_date`, `accrual_amount`, `created_at`) VALUES
(1, 38, 'payé', '2025-05-26', 1.4, '2025-05-26 10:40:28'),
(2, 45, 'payé', '2025-05-26', 1.4, '2025-05-26 10:40:28'),
(3, 40, 'payé', '2025-05-26', 1.4, '2025-05-26 10:40:28'),
(4, 43, 'payé', '2025-05-26', 1.4, '2025-05-26 10:40:28'),
(5, 41, 'payé', '2025-05-26', 1.4, '2025-05-26 10:40:28'),
(6, 39, 'payé', '2025-05-26', 1.4, '2025-05-26 10:40:28'),
(7, 37, 'payé', '2025-05-26', 1.4, '2025-05-26 10:40:28'),
(8, 44, 'payé', '2025-05-26', 1.4, '2025-05-26 10:40:28'),
(9, 42, 'payé', '2025-05-26', 1.4, '2025-05-26 10:40:28'),
(10, 31, 'payé', '2025-05-26', 1.4, '2025-05-26 10:41:31'),
(11, 34, 'payé', '2025-05-26', 1.4, '2025-05-26 10:41:31'),
(12, 36, 'payé', '2025-05-26', 1.4, '2025-05-26 10:41:31'),
(13, 30, 'payé', '2025-05-26', 1.4, '2025-05-26 10:41:31'),
(14, 32, 'payé', '2025-05-26', 1.4, '2025-05-26 10:41:31'),
(15, 29, 'payé', '2025-05-26', 1.4, '2025-05-26 10:41:31'),
(16, 33, 'payé', '2025-05-26', 1.4, '2025-05-26 10:41:31'),
(17, 35, 'payé', '2025-05-26', 1.4, '2025-05-26 10:41:31'),
(18, 26, 'payé', '2025-05-26', 1.4, '2025-05-26 10:44:39'),
(19, 17, 'payé', '2025-05-26', 1.4, '2025-05-26 10:44:40'),
(20, 22, 'payé', '2025-05-26', 1.4, '2025-05-26 10:44:42'),
(21, 28, 'payé', '2025-05-26', 1.4, '2025-05-26 10:44:45'),
(22, 25, 'payé', '2025-05-26', 1.4, '2025-05-26 10:44:45'),
(23, 23, 'payé', '2025-05-26', 1.4, '2025-05-26 10:44:45'),
(24, 21, 'payé', '2025-05-26', 1.4, '2025-05-26 10:44:45'),
(25, 24, 'payé', '2025-05-26', 1.4, '2025-05-26 10:44:45'),
(26, 27, 'payé', '2025-05-26', 1.4, '2025-05-26 10:44:45'),
(27, 4, 'payé', '2025-05-28', 1.99, '2025-05-28 11:03:56');

-- --------------------------------------------------------

--
-- Table structure for table `leave_balances`
--

CREATE TABLE `leave_balances` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `leave_type` enum('payé','exceptionnel','sans solde','maladie','familial') COLLATE utf8mb4_general_ci NOT NULL,
  `annual_allocation` float NOT NULL DEFAULT '0',
  `used_days` float NOT NULL DEFAULT '0',
  `remaining_days` float NOT NULL DEFAULT '0',
  `last_accrual_date` date DEFAULT NULL,
  `accrual_rate` float NOT NULL DEFAULT '0',
  `year` int NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `leave_balances`
--

INSERT INTO `leave_balances` (`id`, `user_id`, `leave_type`, `annual_allocation`, `used_days`, `remaining_days`, `last_accrual_date`, `accrual_rate`, `year`, `created_at`, `updated_at`) VALUES
(1, 20, 'payé', 19, 0, 21, NULL, 1.83, 2025, '2025-05-21 16:24:15', '2025-05-26 16:05:45'),
(2, 20, 'exceptionnel', 3, 0, 3, NULL, 0, 2025, '2025-05-21 16:24:15', '2025-05-22 08:56:43'),
(3, 20, 'sans solde', 999, 0, 999, NULL, 0, 2025, '2025-05-21 16:24:15', '2025-05-26 16:01:33'),
(4, 20, 'maladie', 15, 0, 15, NULL, 0, 2025, '2025-05-21 16:24:15', NULL),
(5, 20, 'familial', 5, 0, 5, NULL, 0, 2025, '2025-05-21 16:24:15', NULL),
(6, 38, 'payé', 19, 0, 20.4, '2025-05-26', 1.4, 2025, '2025-05-21 16:24:15', '2025-05-26 10:40:44'),
(7, 38, 'exceptionnel', 3, 0, 3, NULL, 0, 2025, '2025-05-21 16:24:15', '2025-05-22 08:56:43'),
(8, 38, 'sans solde', 999, 0, 999, NULL, 0, 2025, '2025-05-21 16:24:15', '2025-05-26 10:40:28'),
(9, 38, 'maladie', 15, 0, 15, NULL, 0, 2025, '2025-05-21 16:24:15', NULL),
(10, 38, 'familial', 5, 0, 5, NULL, 0, 2025, '2025-05-21 16:24:15', NULL),
(11, 45, 'payé', 19, 0, 20.4, '2025-05-26', 1.4, 2025, '2025-05-21 16:24:15', '2025-05-26 10:40:44'),
(12, 45, 'exceptionnel', 3, 0, 3, NULL, 0, 2025, '2025-05-21 16:24:15', '2025-05-22 08:56:43'),
(13, 45, 'sans solde', 999, 0, 999, NULL, 0, 2025, '2025-05-21 16:24:15', '2025-05-26 10:40:28'),
(14, 45, 'maladie', 15, 0, 15, NULL, 0, 2025, '2025-05-21 16:24:15', NULL),
(15, 45, 'familial', 5, 0, 5, NULL, 0, 2025, '2025-05-21 16:24:15', NULL),
(16, 18, 'payé', 19, 0, 19, NULL, 1.4, 2025, '2025-05-21 16:24:15', NULL),
(17, 18, 'exceptionnel', 3, 0, 3, NULL, 0, 2025, '2025-05-21 16:24:15', '2025-05-22 08:57:23'),
(18, 18, 'sans solde', 999, 0, 999, NULL, 0, 2025, '2025-05-21 16:24:15', '2025-05-26 16:03:35'),
(19, 18, 'maladie', 15, 0, 15, NULL, 0, 2025, '2025-05-21 16:24:15', NULL),
(20, 18, 'familial', 5, 0, 5, NULL, 0, 2025, '2025-05-21 16:24:15', NULL),
(21, 26, 'payé', 19, 0, 20.4, '2025-05-26', 1.4, 2025, '2025-05-21 16:24:15', '2025-05-26 10:44:45'),
(22, 26, 'exceptionnel', 3, 0, 3, NULL, 0, 2025, '2025-05-21 16:24:15', '2025-05-22 08:57:23'),
(23, 26, 'sans solde', 999, 0, 999, NULL, 0, 2025, '2025-05-21 16:24:15', '2025-05-26 10:44:38'),
(24, 26, 'maladie', 15, 0, 15, NULL, 0, 2025, '2025-05-21 16:24:15', NULL),
(25, 26, 'familial', 5, 0, 5, NULL, 0, 2025, '2025-05-21 16:24:15', NULL),
(26, 31, 'payé', 19, 0, 20.4, '2025-05-26', 1.4, 2025, '2025-05-21 16:24:15', '2025-05-26 10:41:31'),
(27, 31, 'exceptionnel', 3, 0, 3, NULL, 0, 2025, '2025-05-21 16:24:15', '2025-05-22 08:57:23'),
(28, 31, 'sans solde', 999, 0, 999, NULL, 0, 2025, '2025-05-21 16:24:15', '2025-05-26 10:41:31'),
(29, 31, 'maladie', 15, 0, 15, NULL, 0, 2025, '2025-05-21 16:24:15', NULL),
(30, 31, 'familial', 5, 0, 5, NULL, 0, 2025, '2025-05-21 16:24:15', NULL),
(31, 34, 'payé', 19, 0, 20.4, '2025-05-26', 1.4, 2025, '2025-05-21 16:24:15', '2025-05-26 10:44:46'),
(32, 34, 'exceptionnel', 3, 0, 3, NULL, 0, 2025, '2025-05-21 16:24:15', '2025-05-22 08:56:43'),
(33, 34, 'sans solde', 999, 0, 999, NULL, 0, 2025, '2025-05-21 16:24:15', '2025-05-26 10:41:31'),
(34, 34, 'maladie', 15, 0, 15, NULL, 0, 2025, '2025-05-21 16:24:15', NULL),
(35, 34, 'familial', 5, 0, 5, NULL, 0, 2025, '2025-05-21 16:24:15', NULL),
(36, 36, 'payé', 19, 0, 20.4, '2025-05-26', 1.4, 2025, '2025-05-21 16:24:15', '2025-05-26 10:44:46'),
(37, 36, 'exceptionnel', 3, 0, 3, NULL, 0, 2025, '2025-05-21 16:24:15', '2025-05-22 08:56:43'),
(38, 36, 'sans solde', 999, 0, 999, NULL, 0, 2025, '2025-05-21 16:24:15', '2025-05-26 10:41:31'),
(39, 36, 'maladie', 15, 0, 15, NULL, 0, 2025, '2025-05-21 16:24:15', NULL),
(40, 36, 'familial', 5, 0, 5, NULL, 0, 2025, '2025-05-21 16:24:15', NULL),
(41, 46, 'payé', 19, 0, 19, NULL, 1.83, 2025, '2025-05-21 16:24:16', NULL),
(42, 46, 'exceptionnel', 3, 0, 3, NULL, 0, 2025, '2025-05-21 16:24:16', '2025-05-22 08:56:43'),
(43, 46, 'sans solde', 999, 0, 999, NULL, 0, 2025, '2025-05-21 16:24:16', '2025-05-26 16:03:37'),
(44, 46, 'maladie', 15, 0, 15, NULL, 0, 2025, '2025-05-21 16:24:16', NULL),
(45, 46, 'familial', 5, 0, 5, NULL, 0, 2025, '2025-05-21 16:24:16', NULL),
(46, 17, 'payé', 19, 6, 10, '2025-05-26', 1.4, 2025, '2025-05-21 16:24:16', '2025-05-28 16:42:58'),
(47, 17, 'exceptionnel', 3, 0, 3, NULL, 0, 2025, '2025-05-21 16:24:16', '2025-05-22 08:56:43'),
(48, 17, 'sans solde', 999, 1.5, 997, NULL, 0, 2025, '2025-05-21 16:24:16', '2025-05-28 16:58:55'),
(49, 17, 'maladie', 15, 0, 15, NULL, 0, 2025, '2025-05-21 16:24:16', NULL),
(50, 17, 'familial', 5, 0, 5, NULL, 0, 2025, '2025-05-21 16:24:16', NULL),
(51, 22, 'payé', 19, 7, 6.4, '2025-05-26', 1.4, 2025, '2025-05-21 16:24:16', '2025-05-26 10:44:45'),
(52, 22, 'exceptionnel', 3, 1, 2, NULL, 0, 2025, '2025-05-21 16:24:16', '2025-05-26 10:44:41'),
(53, 22, 'sans solde', 999, 0, 999, NULL, 0, 2025, '2025-05-21 16:24:16', '2025-05-26 10:44:41'),
(54, 22, 'maladie', 15, 0, 15, NULL, 0, 2025, '2025-05-21 16:24:16', NULL),
(55, 22, 'familial', 5, 0, 5, NULL, 0, 2025, '2025-05-21 16:24:16', NULL),
(56, 19, 'payé', 19, 13.5, 1, NULL, 1.83, 2025, '2025-05-21 16:24:16', '2025-05-22 14:45:36'),
(57, 19, 'exceptionnel', 3, 0, 3, NULL, 0, 2025, '2025-05-21 16:24:16', '2025-05-22 08:57:23'),
(58, 19, 'sans solde', 999, 0, 999, NULL, 0, 2025, '2025-05-21 16:24:16', '2025-05-26 16:03:38'),
(59, 19, 'maladie', 15, 0, 15, NULL, 0, 2025, '2025-05-21 16:24:16', NULL),
(60, 19, 'familial', 5, 0, 5, NULL, 0, 2025, '2025-05-21 16:24:16', NULL),
(61, 40, 'payé', 19, 0, 20.4, '2025-05-26', 1.4, 2025, '2025-05-21 16:24:16', '2025-05-26 10:40:45'),
(62, 40, 'exceptionnel', 3, 0, 3, NULL, 0, 2025, '2025-05-21 16:24:16', '2025-05-22 08:57:23'),
(63, 40, 'sans solde', 999, 0, 999, NULL, 0, 2025, '2025-05-21 16:24:16', '2025-05-26 10:40:28'),
(64, 40, 'maladie', 15, 0, 15, NULL, 0, 2025, '2025-05-21 16:24:16', NULL),
(65, 40, 'familial', 5, 0, 5, NULL, 0, 2025, '2025-05-21 16:24:16', NULL),
(66, 30, 'payé', 19, 0, 20.4, '2025-05-26', 1.4, 2025, '2025-05-21 16:24:16', '2025-05-26 10:44:46'),
(67, 30, 'exceptionnel', 3, 0, 3, NULL, 0, 2025, '2025-05-21 16:24:17', '2025-05-22 08:57:23'),
(68, 30, 'sans solde', 999, 0, 999, NULL, 0, 2025, '2025-05-21 16:24:17', '2025-05-26 10:41:31'),
(69, 30, 'maladie', 15, 0, 15, NULL, 0, 2025, '2025-05-21 16:24:17', NULL),
(70, 30, 'familial', 5, 0, 5, NULL, 0, 2025, '2025-05-21 16:24:17', NULL),
(71, 28, 'payé', 19, 0, 20.4, '2025-05-26', 1.4, 2025, '2025-05-21 16:24:18', '2025-05-26 10:44:45'),
(72, 28, 'exceptionnel', 3, 0, 3, NULL, 0, 2025, '2025-05-21 16:24:18', '2025-05-22 08:57:23'),
(73, 28, 'sans solde', 999, 0, 999, NULL, 0, 2025, '2025-05-21 16:24:18', '2025-05-26 10:44:43'),
(74, 28, 'maladie', 15, 0, 15, NULL, 0, 2025, '2025-05-21 16:24:18', NULL),
(75, 28, 'familial', 5, 0, 5, NULL, 0, 2025, '2025-05-21 16:24:18', NULL),
(76, 25, 'payé', 19, 0, 20.4, '2025-05-26', 1.4, 2025, '2025-05-21 16:24:18', '2025-05-26 10:44:45'),
(77, 25, 'exceptionnel', 3, 0, 3, NULL, 0, 2025, '2025-05-21 16:24:19', '2025-05-22 08:57:23'),
(78, 25, 'sans solde', 999, 0, 999, NULL, 0, 2025, '2025-05-21 16:24:19', '2025-05-26 10:44:45'),
(79, 25, 'maladie', 15, 0, 15, NULL, 0, 2025, '2025-05-21 16:24:19', NULL),
(80, 25, 'familial', 5, 0, 5, NULL, 0, 2025, '2025-05-21 16:24:19', NULL),
(81, 32, 'payé', 19, 0, 20.4, '2025-05-26', 1.4, 2025, '2025-05-21 16:24:19', '2025-05-26 10:44:46'),
(82, 32, 'exceptionnel', 3, 0, 3, NULL, 0, 2025, '2025-05-21 16:24:20', '2025-05-22 08:57:23'),
(83, 32, 'sans solde', 999, 0, 999, NULL, 0, 2025, '2025-05-21 16:24:20', '2025-05-26 10:41:31'),
(84, 32, 'maladie', 15, 0, 15, NULL, 0, 2025, '2025-05-21 16:24:20', NULL),
(85, 32, 'familial', 5, 0, 5, NULL, 0, 2025, '2025-05-21 16:24:20', NULL),
(86, 43, 'payé', 19, 0, 20.4, '2025-05-26', 1.4, 2025, '2025-05-21 16:24:21', '2025-05-26 10:40:45'),
(87, 43, 'exceptionnel', 3, 0, 3, NULL, 0, 2025, '2025-05-21 16:24:21', '2025-05-22 08:57:23'),
(88, 43, 'sans solde', 999, 0, 999, NULL, 0, 2025, '2025-05-21 16:24:21', '2025-05-26 10:40:28'),
(89, 43, 'maladie', 15, 0, 15, NULL, 0, 2025, '2025-05-21 16:24:21', NULL),
(90, 43, 'familial', 5, 0, 5, NULL, 0, 2025, '2025-05-21 16:24:21', NULL),
(91, 41, 'payé', 19, 0, 20.4, '2025-05-26', 1.4, 2025, '2025-05-21 16:24:21', '2025-05-26 10:40:45'),
(92, 41, 'exceptionnel', 3, 0, 3, NULL, 0, 2025, '2025-05-21 16:24:21', '2025-05-22 08:57:23'),
(93, 41, 'sans solde', 999, 0, 999, NULL, 0, 2025, '2025-05-21 16:24:21', '2025-05-26 10:40:28'),
(94, 41, 'maladie', 15, 0, 15, NULL, 0, 2025, '2025-05-21 16:24:21', NULL),
(95, 41, 'familial', 5, 0, 5, NULL, 0, 2025, '2025-05-21 16:24:21', NULL),
(96, 23, 'payé', 19, 0, 20.4, '2025-05-26', 1.4, 2025, '2025-05-21 16:24:21', '2025-05-26 10:44:45'),
(97, 23, 'exceptionnel', 3, 0, 3, NULL, 0, 2025, '2025-05-21 16:24:21', '2025-05-22 08:57:23'),
(98, 23, 'sans solde', 999, 0, 999, NULL, 0, 2025, '2025-05-21 16:24:21', '2025-05-26 10:44:45'),
(99, 23, 'maladie', 15, 0, 15, NULL, 0, 2025, '2025-05-21 16:24:21', NULL),
(100, 23, 'familial', 5, 0, 5, NULL, 0, 2025, '2025-05-21 16:24:21', NULL),
(101, 29, 'payé', 19, 0, 20.4, '2025-05-26', 1.4, 2025, '2025-05-21 16:24:21', '2025-05-26 10:44:46'),
(102, 29, 'exceptionnel', 3, 0, 3, NULL, 0, 2025, '2025-05-21 16:24:21', '2025-05-22 08:57:23'),
(103, 29, 'sans solde', 999, 0, 999, NULL, 0, 2025, '2025-05-21 16:24:21', '2025-05-26 10:41:31'),
(104, 29, 'maladie', 15, 0, 15, NULL, 0, 2025, '2025-05-21 16:24:21', NULL),
(105, 29, 'familial', 5, 0, 5, NULL, 0, 2025, '2025-05-21 16:24:21', NULL),
(106, 21, 'payé', 19, 0, 20.4, '2025-05-26', 1.4, 2025, '2025-05-21 16:24:21', '2025-05-26 10:44:45'),
(107, 21, 'exceptionnel', 3, 0, 3, NULL, 0, 2025, '2025-05-21 16:24:21', '2025-05-22 08:57:23'),
(108, 21, 'sans solde', 999, 0, 999, NULL, 0, 2025, '2025-05-21 16:24:21', '2025-05-26 10:44:45'),
(109, 21, 'maladie', 15, 0, 15, NULL, 0, 2025, '2025-05-21 16:24:21', NULL),
(110, 21, 'familial', 5, 0, 5, NULL, 0, 2025, '2025-05-21 16:24:21', NULL),
(111, 39, 'payé', 19, 0, 20.4, '2025-05-26', 1.4, 2025, '2025-05-21 16:24:21', '2025-05-26 10:40:45'),
(112, 39, 'exceptionnel', 3, 0, 3, NULL, 0, 2025, '2025-05-21 16:24:21', '2025-05-22 08:57:23'),
(113, 39, 'sans solde', 999, 0, 999, NULL, 0, 2025, '2025-05-21 16:24:21', '2025-05-26 10:40:28'),
(114, 39, 'maladie', 15, 0, 15, NULL, 0, 2025, '2025-05-21 16:24:21', NULL),
(115, 39, 'familial', 5, 0, 5, NULL, 0, 2025, '2025-05-21 16:24:21', NULL),
(116, 24, 'payé', 19, 8, 4.4, '2025-05-26', 1.4, 2025, '2025-05-21 16:24:21', '2025-05-26 10:44:45'),
(117, 24, 'exceptionnel', 3, 0, 3, NULL, 0, 2025, '2025-05-21 16:24:21', '2025-05-22 08:57:23'),
(118, 24, 'sans solde', 999, 0, 999, NULL, 0, 2025, '2025-05-21 16:24:21', '2025-05-26 10:44:45'),
(119, 24, 'maladie', 15, 0, 15, NULL, 0, 2025, '2025-05-21 16:24:21', NULL),
(120, 24, 'familial', 5, 0, 5, NULL, 0, 2025, '2025-05-21 16:24:21', NULL),
(121, 37, 'payé', 19, 0, 20.4, '2025-05-26', 1.4, 2025, '2025-05-21 16:24:21', '2025-05-26 10:40:45'),
(122, 37, 'exceptionnel', 3, 0, 3, NULL, 0, 2025, '2025-05-21 16:24:21', '2025-05-22 08:57:23'),
(123, 37, 'sans solde', 999, 0, 999, NULL, 0, 2025, '2025-05-21 16:24:21', '2025-05-26 10:40:28'),
(124, 37, 'maladie', 15, 0, 15, NULL, 0, 2025, '2025-05-21 16:24:21', NULL),
(125, 37, 'familial', 5, 0, 5, NULL, 0, 2025, '2025-05-21 16:24:21', NULL),
(126, 33, 'payé', 19, 0, 20.4, '2025-05-26', 1.4, 2025, '2025-05-21 16:24:21', '2025-05-26 10:44:46'),
(127, 33, 'exceptionnel', 3, 0, 3, NULL, 0, 2025, '2025-05-21 16:24:21', '2025-05-22 08:57:23'),
(128, 33, 'sans solde', 999, 0, 999, NULL, 0, 2025, '2025-05-21 16:24:21', '2025-05-26 10:41:31'),
(129, 33, 'maladie', 15, 0, 15, NULL, 0, 2025, '2025-05-21 16:24:21', NULL),
(130, 33, 'familial', 5, 0, 5, NULL, 0, 2025, '2025-05-21 16:24:21', NULL),
(131, 44, 'payé', 19, 0, 20.4, '2025-05-26', 1.4, 2025, '2025-05-21 16:24:21', '2025-05-26 10:40:45'),
(132, 44, 'exceptionnel', 3, 0, 3, NULL, 0, 2025, '2025-05-21 16:24:21', '2025-05-22 08:57:23'),
(133, 44, 'sans solde', 999, 0, 999, NULL, 0, 2025, '2025-05-21 16:24:21', '2025-05-26 10:40:28'),
(134, 44, 'maladie', 15, 0, 15, NULL, 0, 2025, '2025-05-21 16:24:21', NULL),
(135, 44, 'familial', 5, 0, 5, NULL, 0, 2025, '2025-05-21 16:24:21', NULL),
(136, 35, 'payé', 19, 0, 20.4, '2025-05-26', 1.4, 2025, '2025-05-21 16:24:21', '2025-05-26 10:44:46'),
(137, 35, 'exceptionnel', 3, 0, 3, NULL, 0, 2025, '2025-05-21 16:24:21', '2025-05-22 08:57:23'),
(138, 35, 'sans solde', 999, 0, 999, NULL, 0, 2025, '2025-05-21 16:24:21', '2025-05-26 10:41:31'),
(139, 35, 'maladie', 15, 0, 15, NULL, 0, 2025, '2025-05-21 16:24:21', NULL),
(140, 35, 'familial', 5, 0, 5, NULL, 0, 2025, '2025-05-21 16:24:21', NULL),
(141, 27, 'payé', 19, 0, 20.4, '2025-05-26', 1.4, 2025, '2025-05-21 16:24:21', '2025-05-26 10:44:45'),
(142, 27, 'exceptionnel', 3, 0, 3, NULL, 0, 2025, '2025-05-21 16:24:21', '2025-05-22 08:57:23'),
(143, 27, 'sans solde', 999, 0, 999, NULL, 0, 2025, '2025-05-21 16:24:21', '2025-05-26 10:44:45'),
(144, 27, 'maladie', 15, 0, 15, NULL, 0, 2025, '2025-05-21 16:24:21', NULL),
(145, 27, 'familial', 5, 0, 5, NULL, 0, 2025, '2025-05-21 16:24:21', NULL),
(146, 4, 'payé', 19, 0, 20.99, '2025-05-28', 1.99, 2025, '2025-05-21 16:24:21', '2025-05-28 13:29:13'),
(147, 4, 'exceptionnel', 3, 0, 3, NULL, 0, 2025, '2025-05-21 16:24:21', '2025-05-22 08:57:23'),
(148, 4, 'sans solde', 999, 0, 999, NULL, 0, 2025, '2025-05-21 16:24:21', '2025-05-26 16:03:46'),
(149, 4, 'maladie', 15, 0, 15, NULL, 0, 2025, '2025-05-21 16:24:21', NULL),
(150, 4, 'familial', 5, 0, 5, NULL, 0, 2025, '2025-05-21 16:24:21', NULL),
(151, 42, 'payé', 19, 0, 20.4, '2025-05-26', 1.4, 2025, '2025-05-21 16:24:21', '2025-05-26 10:40:45'),
(152, 42, 'exceptionnel', 3, 0, 3, NULL, 0, 2025, '2025-05-21 16:24:21', '2025-05-22 08:57:23'),
(153, 42, 'sans solde', 999, 0, 999, NULL, 0, 2025, '2025-05-21 16:24:21', '2025-05-26 10:40:28'),
(154, 42, 'maladie', 15, 0, 15, NULL, 0, 2025, '2025-05-21 16:24:21', NULL),
(155, 42, 'familial', 5, 0, 5, NULL, 0, 2025, '2025-05-21 16:24:21', NULL),
(1136, 48, 'payé', 19, 0, 19, NULL, 1.4, 2025, '2025-05-26 14:14:22', NULL),
(1137, 48, 'exceptionnel', 3, 0, 3, NULL, 0, 2025, '2025-05-26 14:14:22', NULL),
(1138, 48, 'sans solde', 999, 0, 999, NULL, 0, 2025, '2025-05-26 14:14:22', NULL),
(1139, 48, 'maladie', 15, 0, 15, NULL, 0, 2025, '2025-05-26 14:14:22', NULL),
(1140, 48, 'familial', 5, 0, 5, NULL, 0, 2025, '2025-05-26 14:14:22', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `notifications`
--

CREATE TABLE `notifications` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `demande_id` int DEFAULT NULL,
  `type` enum('info','success','error','warning') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'info',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `is_read` tinyint(1) DEFAULT '0',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `notifications`
--

INSERT INTO `notifications` (`id`, `user_id`, `demande_id`, `type`, `title`, `message`, `is_read`, `created_at`) VALUES
(1, 18, NULL, 'info', 'Bienvenue sur Gestion Congés', 'Bienvenue dans votre espace personnel de gestion des congés.', 1, '2025-05-28 13:40:41'),
(2, 17, NULL, 'info', 'Bienvenue sur Gestion Congés', 'Bienvenue dans votre espace personnel de gestion des congés.', 1, '2025-05-28 15:23:19'),
(3, 17, 2, 'error', 'Demande refusée', 'Votre demande de congé [2805-EMP-1] du 28/06/2025 au 30/06/2025 (3 jour(s)) a été refusée. Motif: Période de forte activité dans le service', 1, '2025-05-28 15:24:37'),
(4, 17, 6, 'success', 'Demande approuvée', 'Votre demande de congé [2805-EMP-1] du 04/06/2025 au 06/06/2025 (3 jour(s)) a été définitivement approuvée.', 1, '2025-05-28 16:33:13'),
(5, 17, 3, 'error', 'Demande refusée', 'Votre demande de congé [2805-EMP-1] du 04/06/2025 au 06/06/2025 (3 jour(s)) a été refusée. Motif: Période de forte activité dans le service', 1, '2025-05-28 16:41:17'),
(6, 17, 4, 'error', 'Demande refusée', 'Votre demande de congé [2805-EMP-1] du 04/06/2025 au 06/06/2025 (3 jour(s)) a été refusée. Motif: Période de forte activité dans le service', 1, '2025-05-28 16:41:20'),
(7, 17, 7, 'error', 'Demande refusée', 'Votre demande de congé [2805-EMP-1] du 04/06/2025 au 06/06/2025 (3 jour(s)) a été refusée. Motif: Période de forte activité dans le service', 1, '2025-05-28 16:41:31'),
(8, 17, 5, 'success', 'Demande approuvée', 'Votre demande de congé [2805-EMP-1] du 04/06/2025 au 06/06/2025 (3 jour(s)) a été définitivement approuvée.', 1, '2025-05-28 16:42:58'),
(9, 17, 8, 'error', 'Demande refusée', 'Votre demande de congé [2805-EMP-1] du 30/05/2025 au 02/06/2025 (4 jour(s)) a été refusée. Motif: Solde de congés insuffisant', 1, '2025-05-28 16:43:59'),
(10, 17, 9, 'success', 'Demande approuvée', 'Votre demande de congé [2805-EMP-1] du 29/05/2025 au 29/05/2025 (1 jour(s)) a été définitivement approuvée.', 0, '2025-05-28 16:56:10'),
(11, 17, 9, 'success', 'Demande approuvée', 'Votre demande de congé [2805-EMP-1] du 29/05/2025 au 29/05/2025 (1 jour(s)) a été définitivement approuvée.', 0, '2025-05-28 16:56:42'),
(12, 17, 9, 'success', 'Demande approuvée', 'Votre demande de congé [2805-EMP-1] du 29/05/2025 au 29/05/2025 (1 jour(s)) a été définitivement approuvée.', 0, '2025-05-28 16:58:55');

-- --------------------------------------------------------

--
-- Table structure for table `system_logs`
--

CREATE TABLE `system_logs` (
  `id` int NOT NULL,
  `user_id` int DEFAULT NULL,
  `username` varchar(100) COLLATE utf8mb4_general_ci NOT NULL,
  `action` varchar(50) COLLATE utf8mb4_general_ci NOT NULL,
  `details` text COLLATE utf8mb4_general_ci,
  `ip_address` varchar(45) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int NOT NULL,
  `matricule_rh` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `login` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `nom` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `prenom` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `role` enum('employe','responsable','planificateur','admin') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `departement` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `photo_profil` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `activite` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `date_entree` date DEFAULT NULL,
  `date_creation` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `actif` tinyint(1) DEFAULT '1',
  `telephone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `reset_token` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `reset_expires` datetime DEFAULT NULL,
  `poste` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `manager_id` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `matricule_rh`, `login`, `email`, `password`, `nom`, `prenom`, `role`, `departement`, `photo_profil`, `activite`, `date_entree`, `date_creation`, `actif`, `telephone`, `reset_token`, `reset_expires`, `poste`, `manager_id`) VALUES
(4, '0000', 'admin', '<EMAIL>', '$2y$10$67rqjluJUBM/2CRYRAbARudrjLEpCQ6kbv/LP3QPO2Rnm.l/If3sC', 'Super', 'Admin', 'admin', 'admin', '/uploads/profile_photos/user_4_1747653160.jpeg', NULL, '2025-01-01', '2025-04-17 17:47:54', 1, '+33 6 12 34 56 87', NULL, NULL, 'Adminstrateuur', 4),
(17, 'EMP-1', 'employe1', '<EMAIL>', '$2y$10$67rqjluJUBM/2CRYRAbARudrjLEpCQ6kbv/LP3QPO2Rnm.l/If3sC', 'Dupont', 'Pierre', 'employe', 'Support client', '/uploads/profile_photos/user_17_1747653067.jpeg', 'orange-telco', '2025-03-16', '2025-04-17 17:47:54', 1, '+33 6 12 34 56 78', NULL, NULL, 'employe', 19),
(18, 'PLAN-1', 'planificateur1', '<EMAIL>', '$2y$10$67rqjluJUBM/2CRYRAbARudrjLEpCQ6kbv/LP3QPO2Rnm.l/If3sC', 'Bernard', 'Jacques', 'planificateur', 'Adminstration', '/assets/uploads/profile_photos/user_18_1747758716.jpeg', NULL, '2025-02-04', '2025-04-17 17:47:54', 1, '+33 6 12 34 65 78', NULL, NULL, 'planificateur', 4),
(19, 'RES-1', 'responsable1', '<EMAIL>', '$2y$10$67rqjluJUBM/2CRYRAbARudrjLEpCQ6kbv/LP3QPO2Rnm.l/If3sC', 'Durand', 'Michel', 'responsable', 'Adminstration', '/assets/uploads/profile_photos/user_19_1747819316.jpeg', NULL, '2025-02-19', '2025-04-17 17:47:54', 1, '+33 6 12 43 56 78', NULL, NULL, 'responsable', 4),
(20, 'RES-2', 'responsable2', '<EMAIL>', '$2y$10$67rqjluJUBM/2CRYRAbARudrjLEpCQ6kbv/LP3QPO2Rnm.l/If3sC', 'albert', 'smith', 'responsable', 'Adminstration', '/assets/uploads/profile_photos/user_19_1747819316.jpeg', NULL, '2025-02-19', '2025-04-17 17:47:54', 1, '+33 6 12 43 55 88', NULL, NULL, 'responsable', 4),
(21, 'EMP-2', 'employe2', '<EMAIL>', '$2y$10$67rqjluJUBM/2CRYRAbARudrjLEpCQ6kbv/LP3QPO2Rnm.l/If3sC', 'Martin', 'Jean', 'employe', 'Support client', '/uploads/profile_photos/default.jpeg', NULL, '2025-03-02', '2025-05-21 10:13:31', 1, '+33 6 12 34 56 02', NULL, NULL, 'Employé', 19),
(22, 'EMP-3', 'employe3', '<EMAIL>', '$2y$10$67rqjluJUBM/2CRYRAbARudrjLEpCQ6kbv/LP3QPO2Rnm.l/If3sC', 'Durand', 'Claire', 'employe', 'Support client', '/uploads/profile_photos/default.jpeg', NULL, '2025-03-03', '2025-05-21 10:13:31', 1, '+33 6 12 34 56 03', NULL, NULL, 'Employé', 19),
(23, 'EMP-4', 'employe4', '<EMAIL>', '$2y$10$67rqjluJUBM/2CRYRAbARudrjLEpCQ6kbv/LP3QPO2Rnm.l/If3sC', 'Leroy', 'Lucas', 'employe', 'Support client', '/uploads/profile_photos/default.jpeg', NULL, '2025-03-04', '2025-05-21 10:13:31', 1, '+33 6 12 34 56 04', NULL, NULL, 'Employé', 19),
(24, 'EMP-5', 'employe5', '<EMAIL>', '$2y$10$67rqjluJUBM/2CRYRAbARudrjLEpCQ6kbv/LP3QPO2Rnm.l/If3sC', 'Moreau', 'Emma', 'employe', 'Support client', '/assets/uploads/profile_photos/user_24_1747835821.jpeg', NULL, '2025-03-05', '2025-05-21 10:13:31', 1, '+33 6 12 34 56 05', NULL, NULL, 'Employé', 19),
(25, 'EMP-6', 'employe6', '<EMAIL>', '$2y$10$67rqjluJUBM/2CRYRAbARudrjLEpCQ6kbv/LP3QPO2Rnm.l/If3sC', 'Garcia', 'Hugo', 'employe', 'Support client', '/uploads/profile_photos/default.jpeg', NULL, '2025-03-06', '2025-05-21 10:13:31', 1, '+33 6 12 34 56 06', NULL, NULL, 'Employé', 19),
(26, 'EMP-7', 'employe7', '<EMAIL>', '$2y$10$67rqjluJUBM/2CRYRAbARudrjLEpCQ6kbv/LP3QPO2Rnm.l/If3sC', 'Bernard', 'Léa', 'employe', 'Support client', '/uploads/profile_photos/default.jpeg', NULL, '2025-03-07', '2025-05-21 10:13:31', 1, '+33 6 12 34 56 07', NULL, NULL, 'Employé', 19),
(27, 'EMP-8', 'employe8', '<EMAIL>', '$2y$10$67rqjluJUBM/2CRYRAbARudrjLEpCQ6kbv/LP3QPO2Rnm.l/If3sC', 'Roux', 'Nathan', 'employe', 'Support client', '/uploads/profile_photos/default.jpeg', NULL, '2025-03-08', '2025-05-21 10:13:31', 1, '+33 6 12 34 56 08', NULL, NULL, 'Employé', 19),
(28, 'EMP-9', 'employe9', '<EMAIL>', '$2y$10$67rqjluJUBM/2CRYRAbARudrjLEpCQ6kbv/LP3QPO2Rnm.l/If3sC', 'Fournier', 'Chloé', 'employe', 'Support client', '/uploads/profile_photos/default.jpeg', NULL, '2025-03-09', '2025-05-21 10:13:31', 1, '+33 6 12 34 56 09', NULL, NULL, 'Employé', 19),
(29, 'EMP-10', 'employe10', '<EMAIL>', '$2y$10$67rqjluJUBM/2CRYRAbARudrjLEpCQ6kbv/LP3QPO2Rnm.l/If3sC', 'Lopez', 'Tom', 'employe', 'Support client', '/uploads/profile_photos/default.jpeg', NULL, '2025-03-10', '2025-05-21 10:13:31', 1, '+33 6 12 34 56 10', NULL, NULL, 'Employé', 20),
(30, 'EMP-11', 'employe11', '<EMAIL>', '$2y$10$67rqjluJUBM/2CRYRAbARudrjLEpCQ6kbv/LP3QPO2Rnm.l/If3sC', 'Fontaine', 'Camille', 'employe', 'Support client', '/uploads/profile_photos/default.jpeg', NULL, '2025-03-11', '2025-05-21 10:13:31', 1, '+33 6 12 34 56 11', NULL, NULL, 'Employé', 20),
(31, 'EMP-12', 'employe12', '<EMAIL>', '$2y$10$67rqjluJUBM/2CRYRAbARudrjLEpCQ6kbv/LP3QPO2Rnm.l/If3sC', 'Blanc', 'Louis', 'employe', 'Support client', '/uploads/profile_photos/default.jpeg', NULL, '2025-03-12', '2025-05-21 10:13:31', 1, '+33 6 12 34 56 12', NULL, NULL, 'Employé', 20),
(32, 'EMP-13', 'employe13', '<EMAIL>', '$2y$10$67rqjluJUBM/2CRYRAbARudrjLEpCQ6kbv/LP3QPO2Rnm.l/If3sC', 'Guerin', 'Eva', 'employe', 'Support client', '/uploads/profile_photos/default.jpeg', NULL, '2025-03-13', '2025-05-21 10:13:31', 1, '+33 6 12 34 56 13', NULL, NULL, 'Employé', 20),
(33, 'EMP-14', 'employe14', '<EMAIL>', '$2y$10$67rqjluJUBM/2CRYRAbARudrjLEpCQ6kbv/LP3QPO2Rnm.l/If3sC', 'Perrin', 'Noah', 'employe', 'Support client', '/uploads/profile_photos/default.jpeg', NULL, '2025-03-14', '2025-05-21 10:13:31', 1, '+33 6 12 34 56 14', NULL, NULL, 'Employé', 20),
(34, 'EMP-15', 'employe15', '<EMAIL>', '$2y$10$67rqjluJUBM/2CRYRAbARudrjLEpCQ6kbv/LP3QPO2Rnm.l/If3sC', 'Bonnet', 'Lina', 'employe', 'Support client', '/uploads/profile_photos/default.jpeg', NULL, '2025-03-15', '2025-05-21 10:13:31', 1, '+33 6 12 34 56 15', NULL, NULL, 'Employé', 20),
(35, 'EMP-16', 'employe16', '<EMAIL>', '$2y$10$67rqjluJUBM/2CRYRAbARudrjLEpCQ6kbv/LP3QPO2Rnm.l/If3sC', 'Robin', 'Adam', 'employe', 'Support client', '/uploads/profile_photos/default.jpeg', NULL, '2025-03-16', '2025-05-21 10:13:31', 1, '+33 6 12 34 56 16', NULL, NULL, 'Employé', 20),
(36, 'EMP-17', 'employe17', '<EMAIL>', '$2y$10$67rqjluJUBM/2CRYRAbARudrjLEpCQ6kbv/LP3QPO2Rnm.l/If3sC', 'Chevalier', 'Alice', 'employe', 'Support client', '/uploads/profile_photos/default.jpeg', NULL, '2025-03-17', '2025-05-21 10:13:31', 1, '+33 6 12 34 56 17', NULL, NULL, 'Employé', 20),
(37, 'EMP-18', 'employe18', '<EMAIL>', '$2y$10$67rqjluJUBM/2CRYRAbARudrjLEpCQ6kbv/LP3QPO2Rnm.l/If3sC', 'Muller', 'Maxime', 'employe', 'Support client', '/uploads/profile_photos/default.jpeg', NULL, '2025-03-18', '2025-05-21 10:13:31', 1, '+33 6 12 34 56 18', NULL, NULL, 'Employé', 46),
(38, 'EMP-19', 'employe19', '<EMAIL>', '$2y$10$67rqjluJUBM/2CRYRAbARudrjLEpCQ6kbv/LP3QPO2Rnm.l/If3sC', 'Andre', 'Lucie', 'employe', 'Support client', '/uploads/profile_photos/default.jpeg', NULL, '2025-03-19', '2025-05-21 10:13:31', 1, '+33 6 12 34 56 19', NULL, NULL, 'Employé', 46),
(39, 'EMP-20', 'employe20', '<EMAIL>', '$2y$10$67rqjluJUBM/2CRYRAbARudrjLEpCQ6kbv/LP3QPO2Rnm.l/If3sC', 'Mercier', 'Matteo', 'employe', 'Support client', '/uploads/profile_photos/default.jpeg', NULL, '2025-03-20', '2025-05-21 10:13:31', 1, '+33 6 12 34 56 20', NULL, NULL, 'Employé', 46),
(40, 'EMP-21', 'employe21', '<EMAIL>', '$2y$10$67rqjluJUBM/2CRYRAbARudrjLEpCQ6kbv/LP3QPO2Rnm.l/If3sC', 'Faure', 'Sarah', 'employe', 'Support client', '/uploads/profile_photos/default.jpeg', NULL, '2025-03-21', '2025-05-21 10:13:31', 1, '+33 6 12 34 56 21', NULL, NULL, 'Employé', 46),
(41, 'EMP-22', 'employe22', '<EMAIL>', '$2y$10$67rqjluJUBM/2CRYRAbARudrjLEpCQ6kbv/LP3QPO2Rnm.l/If3sC', 'Leclerc', 'Ethan', 'employe', 'Support client', '/uploads/profile_photos/default.jpeg', NULL, '2025-03-22', '2025-05-21 10:13:31', 1, '+33 6 12 34 56 22', NULL, NULL, 'Employé', 46),
(42, 'EMP-23', 'employe23', '<EMAIL>', '$2y$10$67rqjluJUBM/2CRYRAbARudrjLEpCQ6kbv/LP3QPO2Rnm.l/If3sC', 'Vidal', 'Juliette', 'employe', 'Support client', '/uploads/profile_photos/default.jpeg', NULL, '2025-03-23', '2025-05-21 10:13:31', 1, '+33 6 12 34 56 23', NULL, NULL, 'Employé', 46),
(43, 'EMP-24', 'employe24', '<EMAIL>', '$2y$10$67rqjluJUBM/2CRYRAbARudrjLEpCQ6kbv/LP3QPO2Rnm.l/If3sC', 'Henry', 'Gabriel', 'employe', 'Support client', '/uploads/profile_photos/default.jpeg', NULL, '2025-03-24', '2025-05-21 10:13:31', 1, '+33 6 12 34 56 24', NULL, NULL, 'Employé', 46),
(44, 'EMP-25', 'employe25', '<EMAIL>', '$2y$10$67rqjluJUBM/2CRYRAbARudrjLEpCQ6kbv/LP3QPO2Rnm.l/If3sC', 'Renaud', 'Inès', 'employe', 'Support client', '/uploads/profile_photos/default.jpeg', NULL, '2025-03-25', '2025-05-21 10:13:31', 1, '+33 6 12 34 56 25', NULL, NULL, 'Employé', 46),
(45, 'EMP-26', 'employe26', '<EMAIL>', '$2y$10$67rqjluJUBM/2CRYRAbARudrjLEpCQ6kbv/LP3QPO2Rnm.l/If3sC', 'Barbier', 'Axel', 'employe', 'Support client', '/uploads/profile_photos/default.jpeg', NULL, '2025-03-26', '2025-05-21 10:13:31', 1, '+33 6 12 34 56 26', NULL, NULL, 'Employé', 46),
(46, 'RSC001', 'resp_support', '<EMAIL>', '$2y$10$67rqjluJUBM/2CRYRAbARudrjLEpCQ6kbv/LP3QPO2Rnm.l/If3sC', 'Dubois', 'Sophie', 'responsable', 'Support client', '/assets/uploads/profile_photos/user_46_1747996293.png', NULL, '2020-01-01', '2025-05-21 13:14:20', 1, '+ 3362536258', NULL, NULL, 'responsable', 4),
(47, 'TEST-001', 'test_user_1748251638', '<EMAIL>', '$2y$10$VSa28PMvnSCxHLGe7H9z7OzCchiGVBMgphn2dVYvie92VwTuS62mO', 'Test', 'User', 'employe', 'IT', NULL, NULL, '2025-01-01', '2025-05-26 09:27:18', 0, '+33123456789', NULL, NULL, 'Testeur', NULL),
(48, 'EMP-27', 'ahmed', '<EMAIL>', '$2y$10$iV9N3EJoovhVfUfaofceGe3kmfi0LafkAw.ORmQhyYLtYwA1jMoXC', 'Jaidi', 'ahmed', 'employe', 'IT', NULL, NULL, '2023-10-12', '2025-05-26 09:29:25', 1, '+216 25638574', NULL, NULL, 'developer', NULL);

--
-- Indexes for dumped tables
--

--
-- Indexes for table `demandes_conges`
--
ALTER TABLE `demandes_conges`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `idx_reference_demande` (`reference_demande`),
  ADD KEY `fk_demandes_planificateur` (`planificateur_id`);

--
-- Indexes for table `department_leave_balances`
--
ALTER TABLE `department_leave_balances`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `department_year` (`department_name`,`year`);

--
-- Indexes for table `documents`
--
ALTER TABLE `documents`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `document_requests`
--
ALTER TABLE `document_requests`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_type` (`type`),
  ADD KEY `idx_date_requested` (`date_requested`),
  ADD KEY `fk_document_requests_processor` (`processed_by`);

--
-- Indexes for table `jours_feries`
--
ALTER TABLE `jours_feries`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `leave_accrual_history`
--
ALTER TABLE `leave_accrual_history`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `leave_balances`
--
ALTER TABLE `leave_balances`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_leave_type_year` (`user_id`,`leave_type`,`year`);

--
-- Indexes for table `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `system_logs`
--
ALTER TABLE `system_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_action` (`action`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `login` (`login`),
  ADD UNIQUE KEY `unique_email` (`email`),
  ADD KEY `idx_manager_id` (`manager_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `demandes_conges`
--
ALTER TABLE `demandes_conges`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `department_leave_balances`
--
ALTER TABLE `department_leave_balances`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=28;

--
-- AUTO_INCREMENT for table `documents`
--
ALTER TABLE `documents`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `document_requests`
--
ALTER TABLE `document_requests`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=17;

--
-- AUTO_INCREMENT for table `jours_feries`
--
ALTER TABLE `jours_feries`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=22;

--
-- AUTO_INCREMENT for table `leave_accrual_history`
--
ALTER TABLE `leave_accrual_history`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=28;

--
-- AUTO_INCREMENT for table `leave_balances`
--
ALTER TABLE `leave_balances`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2615;

--
-- AUTO_INCREMENT for table `notifications`
--
ALTER TABLE `notifications`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT for table `system_logs`
--
ALTER TABLE `system_logs`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=49;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `demandes_conges`
--
ALTER TABLE `demandes_conges`
  ADD CONSTRAINT `demandes_conges_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_demandes_planificateur` FOREIGN KEY (`planificateur_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `document_requests`
--
ALTER TABLE `document_requests`
  ADD CONSTRAINT `fk_document_requests_processor` FOREIGN KEY (`processed_by`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_document_requests_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `leave_accrual_history`
--
ALTER TABLE `leave_accrual_history`
  ADD CONSTRAINT `leave_accrual_history_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `leave_balances`
--
ALTER TABLE `leave_balances`
  ADD CONSTRAINT `leave_balances_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `system_logs`
--
ALTER TABLE `system_logs`
  ADD CONSTRAINT `fk_system_logs_users` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

--
-- Constraints for table `users`
--
ALTER TABLE `users`
  ADD CONSTRAINT `fk_manager_id` FOREIGN KEY (`manager_id`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
