/* General body style for all pages */
body {
    margin: 0;
    padding: 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f3f4f6; /* Light gray background */
    min-height: 100vh;
}

/* Login page specific style */
body.login-page {
    height: 100vh;
    background: url('https://images.unsplash.com/photo-1507525428034-b723cf961d3e?auto=format&fit=crop&w=1920&q=80') no-repeat center center fixed;
    background-size: cover;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow-x: hidden;
    padding: 1rem;
    box-sizing: border-box;
}

  /* Conteneur du formulaire de connexion */
  .login-container {
    background: rgba(255, 255, 255, 0.9);
    padding: 40px;
    border-radius: 12px;
    box-shadow: 0 8px 16px rgba(0,0,0,0.25);
    width: 350px;
    text-align: center;
  }

  /* Champs de saisie */
  .login-container input[type="text"],
  .login-container input[type="password"] {
    width: 100%;
    padding: 12px;
    margin: 10px 0;
    border: 1px solid #ccc;
    border-radius: 6px;
  }

  /* Bouton */
  .login-container button {
    width: 100%;
    background-color: #007BFF;
    color: white;
    padding: 12px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 16px;
  }

  .login-container button:hover {
    background-color: #0056b3;
  }

  /* Message d'erreur */
  .error-message {
    color: red;
    margin-top: 10px;
  }

/* Card styling for dashboard and other pages */
.card {
  background-color: white;
  border-radius: 0.75rem;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
  transition: all 0.2s ease;
  margin-bottom: 1rem;
  border: 1px solid rgba(0, 0, 0, 0.05);
  width: 100%;
}

/* Only apply hover effects on devices that support hover */
@media (hover: hover) {
  .card:hover {
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
    transform: translateY(-2px);
  }
}

.card h2 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  color: #1e293b;
  word-break: break-word;
}

.card h2 i {
  margin-right: 0.5rem;
  flex-shrink: 0;
}

/* Responsive card adjustments */
@media (max-width: 640px) {
  .card {
    padding: 1rem;
  }

  .card h2 {
    font-size: 1rem;
  }
}

/* Apply card styling to other elements that should look like cards */
.bg-white.p-6.rounded-lg.shadow-sm,
.bg-white.rounded-lg.shadow-sm,
.bg-white.rounded-lg.shadow,
.bg-white.rounded-md.shadow,
.bg-white.shadow,
.card-style {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08) !important;
  transition: all 0.2s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
  border-radius: 0.75rem;
}

/* Chart container styling */
.chart-container {
  position: relative;
  height: 300px;
  width: 100%;
  max-height: 400px;
  margin: 0 auto;
}

/* Responsive chart adjustments */
@media (max-width: 1200px) {
  .chart-container {
    height: 250px;
  }
}

@media (max-width: 768px) {
  .chart-container {
    height: 220px;
  }
}

@media (max-width: 640px) {
  .chart-container {
    height: 200px;
  }

  /* Ensure proper spacing in grid layouts on mobile */
  .grid {
    gap: 1rem !important;
  }

  /* Adjust padding for card containers on mobile */
  .bg-white.p-6 {
    padding: 1rem !important;
  }
}

.bg-white.p-6.rounded-lg.shadow-sm:hover,
.bg-white.rounded-lg.shadow-sm:hover,
.bg-white.rounded-lg.shadow:hover,
.bg-white.rounded-md.shadow:hover,
.bg-white.shadow:hover,
.card-style:hover {
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05) !important;
  transform: translateY(-2px);
}

/* Table card styling */
.table-card {
  background-color: white;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.table-card table {
  width: 100%;
  min-width: 640px; /* Ensures table doesn't get too compressed */
}

.table-card thead tr {
  background-color: #f8fafc;
}

.table-card th {
  padding: 0.75rem 1rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  color: #64748b;
  text-align: left;
  white-space: nowrap;
}

.table-card td {
  padding: 0.75rem 1rem;
  border-top: 1px solid #f1f5f9;
}

/* Responsive table adjustments */
@media (max-width: 640px) {
  .table-card {
    border-radius: 0.5rem;
  }

  .table-card th,
  .table-card td {
    padding: 0.625rem 0.75rem;
  }

  /* Responsive table for very small screens */
  .table-responsive-stack {
    display: block;
  }

  .table-responsive-stack thead {
    display: none;
  }

  .table-responsive-stack tr {
    display: block;
    border-bottom: 1px solid #f1f5f9;
    margin-bottom: 0.5rem;
  }

  .table-responsive-stack td {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: none;
    padding: 0.5rem 0.75rem;
  }

  .table-responsive-stack td:before {
    content: attr(data-label);
    font-weight: 600;
    margin-right: 0.5rem;
  }
}

/* Modal card styling */
.modal-card {
  background-color: white;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(0, 0, 0, 0.05);
  width: 100%;
  max-width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
  box-sizing: border-box;
}

/* Confirmation Modal Styling */
.confirmation-modal-card {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(0, 0, 0, 0.05);
  max-width: 28rem;
  width: 100%;
  transform: scale(0.95);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.confirmation-modal-card.modal-show {
  transform: scale(1);
  opacity: 1;
}

/* Modal backdrop animation */
#confirmationModal {
  backdrop-filter: blur(4px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

#confirmationModal.modal-show {
  opacity: 1;
}

/* Policy Modal specific styling */
#policyModal {
  backdrop-filter: blur(4px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

#policyModal.modal-show {
  opacity: 1;
}

/* Modal animation utilities */
.scale-95 {
  transform: scale(0.95);
}

.scale-100 {
  transform: scale(1);
}

.opacity-0 {
  opacity: 0;
}

.opacity-100 {
  opacity: 1;
}

/* Modal icon styling for different types */
.modal-icon-warning {
  background-color: #fef3c7;
  color: #d97706;
}

.modal-icon-danger {
  background-color: #fee2e2;
  color: #dc2626;
}

.modal-icon-success {
  background-color: #d1fae5;
  color: #059669;
}

.modal-icon-info {
  background-color: #dbeafe;
  color: #2563eb;
}

.modal-icon-question {
  background-color: #e0e7ff;
  color: #7c3aed;
}

/* Modal button styling */
.modal-btn-primary {
  background-color: #7c3aed;
  border-color: #7c3aed;
  color: white;
}

.modal-btn-primary:hover {
  background-color: #6d28d9;
  border-color: #6d28d9;
}

.modal-btn-primary:focus {
  ring-color: #7c3aed;
}

.modal-btn-danger {
  background-color: #dc2626;
  border-color: #dc2626;
  color: white;
}

.modal-btn-danger:hover {
  background-color: #b91c1c;
  border-color: #b91c1c;
}

.modal-btn-danger:focus {
  ring-color: #dc2626;
}

.modal-btn-success {
  background-color: #059669;
  border-color: #059669;
  color: white;
}

.modal-btn-success:hover {
  background-color: #047857;
  border-color: #047857;
}

.modal-btn-success:focus {
  ring-color: #059669;
}

/* Responsive modal adjustments */
@media (max-width: 640px) {
  .modal-card {
    max-width: 95vw;
    width: calc(100% - 2rem);
    border-radius: 0.5rem;
    margin: 1rem;
  }

  .modal-card .p-6 {
    padding: 1rem !important;
  }

  .modal-card input,
  .modal-card select,
  .modal-card textarea {
    font-size: 16px !important; /* Prevents iOS zoom on focus */
    max-width: 100%;
    width: 100%;
    box-sizing: border-box;
  }

  /* Policy modal specific mobile adjustments */
  #policyModal .modal-card {
    max-height: 90vh;
    overflow-y: auto;
  }

  #policyModal .bg-purple-600 {
    padding: 1rem !important;
  }

  #policyModal .space-y-4 > div {
    padding: 0.75rem !important;
  }

  #policyModal .text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
}

/* Sidebar styling */
.sidebar {
  width: 240px;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  background-color: #1e293b; /* Dark blue/indigo color */
  color: white;
  transition: all 0.3s ease;
  z-index: 100;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.sidebar-collapsed {
  width: 60px;
}

.sidebar-collapsed .logo-text {
  display: none;
}

/* Logo and header area */
.sidebar-header {
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sidebar-logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.logo-text {
  font-size: 1.25rem;
  font-weight: 700;
  color: white;
}

#toggleSidebar {
  color: white;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.375rem;
  transition: background-color 0.2s ease;
}

#toggleSidebar:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Menu items styling */
.sidebar-nav {
  padding: 0.5rem;
  margin-top: 1rem;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  color: #e2e8f0; /* Light gray text for better contrast */
  text-decoration: none;
  border-radius: 0.375rem;
  margin-bottom: 0.5rem;
  transition: all 0.2s ease;
  position: relative;
}

.menu-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
}

.menu-item i {
  margin-right: 0.75rem;
  width: 1.25rem;
  text-align: center;
  font-size: 1.125rem;
  min-width: 24px;
}

.sidebar-collapsed .menu-text {
  display: none;
}

/* Category headings */
.sidebar-category {
  font-size: 0.75rem;
  text-transform: uppercase;
  color: #94a3b8; /* Lighter blue-gray for better contrast */
  font-weight: 600;
  padding: 0.75rem 1rem;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
}

.sidebar-collapsed .sidebar-category {
  display: none;
}

/* Notification badge */
.notification-badge {
  position: absolute;
  right: 10px;
  background-color: #ef4444; /* Red color */
  color: white;
  font-size: 0.75rem;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Collapsible menu group styling */
.menu-item-group {
  margin-bottom: 0.5rem;
}

.menu-item-toggle {
  position: relative;
  justify-content: space-between;
}

.toggle-icon {
  font-size: 0.875rem;
  transition: transform 0.2s ease;
  margin-left: auto;
  margin-right: 0;
}

.menu-item-toggle:hover .toggle-icon {
  color: white;
}

/* Submenu styling */
.submenu {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 0.375rem;
  margin-top: 0.25rem;
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}

.submenu-open {
  max-height: 200px; /* Adjust based on content */
}

.submenu-item {
  display: flex;
  align-items: center;
  padding: 0.625rem 1rem;
  color: #cbd5e1; /* Lighter gray for submenu items */
  text-decoration: none;
  border-radius: 0.25rem;
  margin: 0.25rem 0;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.submenu-item:hover {
  background-color: rgba(255, 255, 255, 0.15);
  color: white;
  transform: translateX(4px);
}

.submenu-item i {
  margin-right: 0.75rem;
  width: 1rem;
  text-align: center;
  font-size: 1rem;
  min-width: 20px;
}

/* Collapsed sidebar adjustments for submenu */
.sidebar-collapsed .submenu {
  display: none;
}

.sidebar-collapsed .toggle-icon {
  display: none;
}

/* Main content area */
#main-content {
  margin-left: 240px;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

/* Responsive adjustments */
/* Small screens (mobile) */
@media (max-width: 640px) {
  .sidebar {
    width: 0;
    transform: translateX(-100%);
    z-index: 50;
  }

  .sidebar.sidebar-open {
    width: 240px;
    transform: translateX(0);
  }

  #main-content {
    margin-left: 0 !important;
    padding: 1rem;
  }

  .mobile-header {
    display: flex;
    padding: 0.75rem 1rem;
    background-color: #1e293b;
    color: white;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 40;
  }

  .mobile-menu-button {
    display: block;
  }

  /* Adjust card padding for mobile */
  .card {
    padding: 1rem;
  }

  /* Make form inputs larger for touch */
  input, select, textarea, button {
    min-height: 44px;
  }

  /* Adjust table display for mobile */
  .table-responsive {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  /* Stack form fields vertically */
  .grid.grid-cols-1.md\:grid-cols-2 {
    grid-template-columns: 1fr !important;
  }

  /* Mobile submenu adjustments */
  .submenu-item {
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
  }

  .submenu-item i {
    margin-right: 0.5rem;
    width: 0.875rem;
    font-size: 0.875rem;
  }
}

/* Medium screens (tablets) */
@media (min-width: 641px) and (max-width: 1024px) {
  .sidebar {
    width: 60px;
  }

  .sidebar.sidebar-open {
    width: 240px;
  }

  .sidebar .menu-text {
    display: none;
  }

  .sidebar.sidebar-open .menu-text {
    display: inline;
  }

  #main-content {
    margin-left: 60px;
    padding: 1.25rem;
  }

  .mobile-header {
    display: none;
  }

  /* Tablet submenu adjustments */
  .sidebar .submenu {
    display: none;
  }

  .sidebar.sidebar-open .submenu {
    display: block;
  }

  .sidebar .toggle-icon {
    display: none;
  }

  .sidebar.sidebar-open .toggle-icon {
    display: inline;
  }

  /* Adjust card grid for tablets */
  .grid.grid-cols-1.md\:grid-cols-2.lg\:grid-cols-3 {
    grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
  }
}

/* Large screens (desktops) */
@media (min-width: 1025px) {
  .sidebar {
    width: 240px;
  }

  #main-content {
    margin-left: 240px;
  }

  .mobile-header {
    display: none;
  }

  .sidebar-collapsed {
    width: 60px;
  }

  .sidebar-collapsed .menu-text {
    display: none;
  }
}

/* Extra large screens */
@media (min-width: 1280px) {
  #main-content {
    padding: 1.5rem 2rem;
  }
}

/* Ensure proper scrolling for tables on all devices */
.table-card {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

/* Login form specific styles */
.login-page input[type="text"],
.login-page input[type="password"],
.login-page input[type="email"] {
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  min-height: 44px;
}

/* Profile page styles */
.profile-info .info-item {
  margin-bottom: 0.75rem;
}

.profile-info .info-value {
  word-break: break-word;
  overflow-wrap: break-word;
  max-width: 100%;
  overflow: hidden;
}

/* Touch-friendly form elements for all screen sizes */
@media (hover: none) and (pointer: coarse) {
  input[type="checkbox"],
  input[type="radio"] {
    min-width: 20px;
    min-height: 20px;
  }

  button,
  .button,
  a.button,
  input[type="button"],
  input[type="submit"] {
    padding: 0.625rem 1rem;
  }
}